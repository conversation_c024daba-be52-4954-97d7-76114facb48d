/**
 * Tiered Performance Manager
 * 
 * Manages performance tiers for different user types with optimized
 * processing speeds, quality levels, and feature access.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

/**
 * Tiered Performance Manager class
 */
export class TieredPerformanceManager {
    constructor() {
        this.initializeTierConfigurations();
        this.initializeFeatureMatrix();
        this.initializePerformanceOptimizations();
    }

    /**
     * Initialize tier configurations
     */
    initializeTierConfigurations() {
        this.tierConfigs = {
            freemium: {
                name: 'Freemium',
                displayName: 'Free Tier',
                priority: 1,
                
                // Processing limits
                processing: {
                    maxPasses: 2,
                    aggressiveness: 0.6,
                    timeout: 10000, // 10 seconds
                    concurrency: 1,
                    maxTextLength: 5000,
                    maxBatchSize: 5
                },
                
                // Quality settings
                quality: {
                    targetDetection: 15, // Less strict target
                    enableAdvancedSynonyms: false,
                    enableSentenceTransformation: true,
                    enableHesitationMarkers: false,
                    qualityValidation: 'basic'
                },
                
                // Rate limits
                rateLimits: {
                    requestsPerMinute: 10,
                    requestsPerHour: 100,
                    requestsPerDay: 500,
                    batchesPerHour: 5
                },
                
                // Features
                features: {
                    basicHumanization: true,
                    aiDetectionAnalysis: true,
                    qualityValidation: false,
                    batchProcessing: false,
                    priorityProcessing: false,
                    advancedAnalytics: false,
                    customSettings: false,
                    apiAccess: false
                }
            },

            premium: {
                name: 'Premium',
                displayName: 'Premium Tier',
                priority: 2,
                
                // Processing limits
                processing: {
                    maxPasses: 3,
                    aggressiveness: 0.8,
                    timeout: 15000, // 15 seconds
                    concurrency: 3,
                    maxTextLength: 20000,
                    maxBatchSize: 25
                },
                
                // Quality settings
                quality: {
                    targetDetection: 10, // Standard target
                    enableAdvancedSynonyms: true,
                    enableSentenceTransformation: true,
                    enableHesitationMarkers: true,
                    qualityValidation: 'comprehensive'
                },
                
                // Rate limits
                rateLimits: {
                    requestsPerMinute: 100,
                    requestsPerHour: 1000,
                    requestsPerDay: 10000,
                    batchesPerHour: 50
                },
                
                // Features
                features: {
                    basicHumanization: true,
                    aiDetectionAnalysis: true,
                    qualityValidation: true,
                    batchProcessing: true,
                    priorityProcessing: true,
                    advancedAnalytics: true,
                    customSettings: true,
                    apiAccess: true
                }
            },

            admin: {
                name: 'Admin',
                displayName: 'Administrator',
                priority: 3,
                
                // Processing limits
                processing: {
                    maxPasses: 4,
                    aggressiveness: 0.9,
                    timeout: 20000, // 20 seconds
                    concurrency: 5,
                    maxTextLength: 50000,
                    maxBatchSize: 100
                },
                
                // Quality settings
                quality: {
                    targetDetection: 5, // Strictest target
                    enableAdvancedSynonyms: true,
                    enableSentenceTransformation: true,
                    enableHesitationMarkers: true,
                    qualityValidation: 'comprehensive'
                },
                
                // Rate limits
                rateLimits: {
                    requestsPerMinute: 1000,
                    requestsPerHour: 10000,
                    requestsPerDay: 100000,
                    batchesPerHour: 500
                },
                
                // Features
                features: {
                    basicHumanization: true,
                    aiDetectionAnalysis: true,
                    qualityValidation: true,
                    batchProcessing: true,
                    priorityProcessing: true,
                    advancedAnalytics: true,
                    customSettings: true,
                    apiAccess: true
                }
            }
        };
    }

    /**
     * Initialize feature access matrix
     */
    initializeFeatureMatrix() {
        this.featureMatrix = {
            // Core humanization features
            synonymReplacement: {
                freemium: { enabled: true, quality: 'basic' },
                premium: { enabled: true, quality: 'advanced' },
                admin: { enabled: true, quality: 'premium' }
            },
            
            sentenceTransformation: {
                freemium: { enabled: true, techniques: ['basic'] },
                premium: { enabled: true, techniques: ['basic', 'advanced'] },
                admin: { enabled: true, techniques: ['basic', 'advanced', 'experimental'] }
            },
            
            hesitationMarkers: {
                freemium: { enabled: false },
                premium: { enabled: true, frequency: 0.03 },
                admin: { enabled: true, frequency: 0.05 }
            },
            
            // Analysis features
            aiDetectionAnalysis: {
                freemium: { enabled: true, depth: 'basic' },
                premium: { enabled: true, depth: 'comprehensive' },
                admin: { enabled: true, depth: 'comprehensive' }
            },
            
            qualityValidation: {
                freemium: { enabled: false },
                premium: { enabled: true, level: 'standard' },
                admin: { enabled: true, level: 'comprehensive' }
            },
            
            // Processing features
            batchProcessing: {
                freemium: { enabled: false },
                premium: { enabled: true, maxSize: 25 },
                admin: { enabled: true, maxSize: 100 }
            },
            
            priorityProcessing: {
                freemium: { enabled: false },
                premium: { enabled: true, priority: 'normal' },
                admin: { enabled: true, priority: 'high' }
            }
        };
    }

    /**
     * Initialize performance optimizations
     */
    initializePerformanceOptimizations() {
        this.performanceOptimizations = {
            freemium: {
                caching: {
                    enabled: true,
                    ttl: 300, // 5 minutes
                    maxSize: 100
                },
                processing: {
                    enableParallelization: false,
                    enableOptimizations: false,
                    useSimplifiedAlgorithms: true
                },
                monitoring: {
                    enableDetailedLogging: false,
                    enablePerformanceMetrics: false
                }
            },
            
            premium: {
                caching: {
                    enabled: true,
                    ttl: 600, // 10 minutes
                    maxSize: 500
                },
                processing: {
                    enableParallelization: true,
                    enableOptimizations: true,
                    useSimplifiedAlgorithms: false
                },
                monitoring: {
                    enableDetailedLogging: true,
                    enablePerformanceMetrics: true
                }
            },
            
            admin: {
                caching: {
                    enabled: true,
                    ttl: 1200, // 20 minutes
                    maxSize: 1000
                },
                processing: {
                    enableParallelization: true,
                    enableOptimizations: true,
                    useSimplifiedAlgorithms: false
                },
                monitoring: {
                    enableDetailedLogging: true,
                    enablePerformanceMetrics: true
                }
            }
        };
    }

    /**
     * Get tier configuration for a user
     * @param {string} userTier - User tier (freemium, premium, admin)
     * @returns {Object} Tier configuration
     */
    getTierConfig(userTier) {
        const tier = userTier || 'freemium';
        return this.tierConfigs[tier] || this.tierConfigs.freemium;
    }

    /**
     * Check if user has access to a feature
     * @param {string} userTier - User tier
     * @param {string} feature - Feature name
     * @returns {Object} Feature access info
     */
    checkFeatureAccess(userTier, feature) {
        const tier = userTier || 'freemium';
        const featureConfig = this.featureMatrix[feature];
        
        if (!featureConfig) {
            return { enabled: false, reason: 'Feature not found' };
        }

        const tierFeature = featureConfig[tier];
        if (!tierFeature) {
            return { enabled: false, reason: 'Tier not supported' };
        }

        return {
            enabled: tierFeature.enabled,
            config: tierFeature,
            tier
        };
    }

    /**
     * Get processing configuration for user tier
     * @param {string} userTier - User tier
     * @param {Object} userOptions - User-provided options
     * @returns {Object} Optimized processing configuration
     */
    getProcessingConfig(userTier, userOptions = {}) {
        const tierConfig = this.getTierConfig(userTier);
        const optimizations = this.performanceOptimizations[userTier] || this.performanceOptimizations.freemium;

        // Merge tier defaults with user options (respecting tier limits)
        const processingConfig = {
            // Core processing settings
            maxPasses: Math.min(userOptions.maxPasses || tierConfig.processing.maxPasses, tierConfig.processing.maxPasses),
            aggressiveness: Math.min(userOptions.aggressiveness || tierConfig.processing.aggressiveness, tierConfig.processing.aggressiveness),
            timeout: Math.min(userOptions.timeout || tierConfig.processing.timeout, tierConfig.processing.timeout),
            
            // Quality settings
            targetDetection: Math.max(userOptions.targetDetection || tierConfig.quality.targetDetection, tierConfig.quality.targetDetection),
            enableAdvancedSynonyms: tierConfig.quality.enableAdvancedSynonyms && (userOptions.enableAdvancedSynonyms !== false),
            enableSentenceTransformation: tierConfig.quality.enableSentenceTransformation && (userOptions.enableSentenceTransformation !== false),
            enableHesitationMarkers: tierConfig.quality.enableHesitationMarkers && (userOptions.enableHesitationMarkers !== false),
            
            // Performance optimizations
            enableParallelization: optimizations.processing.enableParallelization,
            enableOptimizations: optimizations.processing.enableOptimizations,
            useSimplifiedAlgorithms: optimizations.processing.useSimplifiedAlgorithms,
            
            // Caching settings
            caching: optimizations.caching,
            
            // Monitoring settings
            monitoring: optimizations.monitoring,
            
            // Tier info
            userTier,
            tierPriority: tierConfig.priority
        };

        return processingConfig;
    }

    /**
     * Validate request against tier limits
     * @param {string} userTier - User tier
     * @param {Object} request - Request details
     * @returns {Object} Validation result
     */
    validateRequest(userTier, request) {
        const tierConfig = this.getTierConfig(userTier);
        const validation = {
            valid: true,
            errors: [],
            warnings: []
        };

        // Check text length
        if (request.textLength > tierConfig.processing.maxTextLength) {
            validation.valid = false;
            validation.errors.push(`Text length (${request.textLength}) exceeds tier limit (${tierConfig.processing.maxTextLength})`);
        }

        // Check batch size
        if (request.batchSize && request.batchSize > tierConfig.processing.maxBatchSize) {
            validation.valid = false;
            validation.errors.push(`Batch size (${request.batchSize}) exceeds tier limit (${tierConfig.processing.maxBatchSize})`);
        }

        // Check feature access
        if (request.features) {
            for (const feature of request.features) {
                const featureAccess = this.checkFeatureAccess(userTier, feature);
                if (!featureAccess.enabled) {
                    validation.valid = false;
                    validation.errors.push(`Feature '${feature}' not available for ${userTier} tier`);
                }
            }
        }

        // Add warnings for suboptimal settings
        if (request.aggressiveness > tierConfig.processing.aggressiveness) {
            validation.warnings.push(`Aggressiveness reduced to tier maximum (${tierConfig.processing.aggressiveness})`);
        }

        return validation;
    }

    /**
     * Get tier comparison for upgrade recommendations
     * @param {string} currentTier - Current user tier
     * @returns {Object} Tier comparison
     */
    getTierComparison(currentTier = 'freemium') {
        const tiers = ['freemium', 'premium', 'admin'];
        const currentIndex = tiers.indexOf(currentTier);
        
        const comparison = {
            current: this.tierConfigs[currentTier],
            upgrades: []
        };

        // Add upgrade options
        for (let i = currentIndex + 1; i < tiers.length; i++) {
            const upgradeTier = tiers[i];
            const upgradeConfig = this.tierConfigs[upgradeTier];
            
            comparison.upgrades.push({
                tier: upgradeTier,
                config: upgradeConfig,
                benefits: this.calculateUpgradeBenefits(currentTier, upgradeTier)
            });
        }

        return comparison;
    }

    /**
     * Calculate upgrade benefits between tiers
     * @param {string} fromTier - Current tier
     * @param {string} toTier - Target tier
     * @returns {Array} List of benefits
     */
    calculateUpgradeBenefits(fromTier, toTier) {
        const fromConfig = this.tierConfigs[fromTier];
        const toConfig = this.tierConfigs[toTier];
        const benefits = [];

        // Processing improvements
        if (toConfig.processing.maxPasses > fromConfig.processing.maxPasses) {
            benefits.push(`Increased processing passes: ${fromConfig.processing.maxPasses} → ${toConfig.processing.maxPasses}`);
        }

        if (toConfig.processing.aggressiveness > fromConfig.processing.aggressiveness) {
            benefits.push(`Higher aggressiveness: ${fromConfig.processing.aggressiveness} → ${toConfig.processing.aggressiveness}`);
        }

        if (toConfig.processing.maxTextLength > fromConfig.processing.maxTextLength) {
            benefits.push(`Larger text limit: ${fromConfig.processing.maxTextLength} → ${toConfig.processing.maxTextLength} characters`);
        }

        // Feature improvements
        const fromFeatures = fromConfig.features;
        const toFeatures = toConfig.features;

        for (const [feature, enabled] of Object.entries(toFeatures)) {
            if (enabled && !fromFeatures[feature]) {
                benefits.push(`New feature: ${feature.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
            }
        }

        // Rate limit improvements
        if (toConfig.rateLimits.requestsPerMinute > fromConfig.rateLimits.requestsPerMinute) {
            benefits.push(`Higher rate limits: ${fromConfig.rateLimits.requestsPerMinute} → ${toConfig.rateLimits.requestsPerMinute} requests/minute`);
        }

        return benefits;
    }

    /**
     * Get usage statistics for a tier
     * @param {string} userTier - User tier
     * @returns {Object} Usage statistics
     */
    getUsageStats(userTier) {
        const tierConfig = this.getTierConfig(userTier);
        
        return {
            tier: userTier,
            limits: tierConfig.rateLimits,
            features: Object.entries(tierConfig.features).filter(([, enabled]) => enabled).map(([feature]) => feature),
            processing: {
                maxPasses: tierConfig.processing.maxPasses,
                maxTextLength: tierConfig.processing.maxTextLength,
                maxBatchSize: tierConfig.processing.maxBatchSize,
                timeout: tierConfig.processing.timeout
            },
            quality: tierConfig.quality
        };
    }

    /**
     * Get all available tiers
     * @returns {Array} Array of tier configurations
     */
    getAllTiers() {
        return Object.entries(this.tierConfigs).map(([tier, config]) => ({
            tier,
            ...config
        }));
    }
}

// Export singleton instance
export const tieredPerformanceManager = new TieredPerformanceManager();
