/**
 * Netlify Function: Advanced Text Humanization
 * 
 * Serverless function for comprehensive text humanization with tiered performance.
 * Implements ES modules compatibility and supports freemium, premium, and admin tiers.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import { HumanizationService } from '../../src/services/humanizationService.js';

// Initialize humanization service
const humanizationService = new HumanizationService();

/**
 * Main Netlify function handler
 * @param {Object} event - Netlify event object
 * @param {Object} context - Netlify context object
 * @returns {Promise<Object>} Response object
 */
export const handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Only allow POST requests
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Method not allowed. Use POST.'
            })
        };
    }

    try {
        // Parse request body
        let requestData;
        try {
            requestData = JSON.parse(event.body);
        } catch (parseError) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Invalid JSON in request body'
                })
            };
        }

        // Validate required fields
        const { text, options = {} } = requestData;
        
        if (!text || typeof text !== 'string') {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Text field is required and must be a string'
                })
            };
        }

        // Validate text length
        if (text.length > 50000) { // 50KB limit for serverless
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Text too long. Maximum 50,000 characters allowed.'
                })
            };
        }

        if (text.length < 10) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Text too short. Minimum 10 characters required.'
                })
            };
        }

        // Extract user tier from headers or options
        const userTier = extractUserTier(event.headers, options);
        
        // Rate limiting check (basic implementation)
        const rateLimitResult = await checkRateLimit(event, userTier);
        if (!rateLimitResult.allowed) {
            return {
                statusCode: 429,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Rate limit exceeded. Please try again later.',
                    retryAfter: rateLimitResult.retryAfter
                })
            };
        }

        // Prepare humanization options
        const humanizationOptions = {
            userTier,
            targetDetection: options.targetDetection || 10,
            maintainTone: options.maintainTone !== false,
            preserveFormatting: options.preserveFormatting !== false,
            aggressiveness: Math.min(Math.max(options.aggressiveness || 0.7, 0.1), 1.0),
            ...options
        };

        // Perform humanization
        const startTime = Date.now();
        const result = await humanizationService.humanizeText(text, humanizationOptions);
        const totalProcessingTime = Date.now() - startTime;

        // Add metadata
        const response = {
            ...result,
            metadata: {
                functionVersion: '2.0.0',
                processingTime: totalProcessingTime,
                userTier,
                timestamp: new Date().toISOString(),
                serverless: true,
                environment: 'netlify'
            }
        };

        // Log usage for analytics (in production, send to analytics service)
        await logUsage({
            userTier,
            textLength: text.length,
            processingTime: totalProcessingTime,
            success: result.success,
            timestamp: new Date().toISOString()
        });

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(response)
        };

    } catch (error) {
        console.error('Humanization function error:', error);
        
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Internal server error. Please try again later.',
                errorId: generateErrorId()
            })
        };
    }
};

/**
 * Extract user tier from request
 * @param {Object} headers - Request headers
 * @param {Object} options - Request options
 * @returns {string} User tier
 */
function extractUserTier(headers, options) {
    // Check for tier in options first
    if (options.userTier && ['freemium', 'premium', 'admin'].includes(options.userTier)) {
        return options.userTier;
    }

    // Check authorization header for premium/admin users
    const authHeader = headers.authorization || headers.Authorization;
    if (authHeader) {
        // In production, validate JWT token and extract tier
        // For now, simple tier detection based on header presence
        if (authHeader.includes('admin')) return 'admin';
        if (authHeader.includes('premium')) return 'premium';
    }

    // Check for API key in headers
    const apiKey = headers['x-api-key'] || headers['X-API-Key'];
    if (apiKey) {
        // In production, validate API key and determine tier
        // For now, assume premium for API key users
        return 'premium';
    }

    // Default to freemium
    return 'freemium';
}

/**
 * Basic rate limiting implementation
 * @param {Object} event - Netlify event
 * @param {string} userTier - User tier
 * @returns {Promise<Object>} Rate limit result
 */
async function checkRateLimit(event, userTier) {
    // In production, use Redis or similar for distributed rate limiting
    // For now, basic implementation using in-memory storage
    
    const rateLimits = {
        freemium: { requests: 10, window: 60000 }, // 10 requests per minute
        premium: { requests: 100, window: 60000 }, // 100 requests per minute
        admin: { requests: 1000, window: 60000 }   // 1000 requests per minute
    };

    const limit = rateLimits[userTier] || rateLimits.freemium;
    
    // Get client identifier
    const clientId = getClientIdentifier(event);
    
    // For serverless, we'll allow all requests for now
    // In production, implement proper distributed rate limiting
    return {
        allowed: true,
        remaining: limit.requests,
        resetTime: Date.now() + limit.window
    };
}

/**
 * Get client identifier for rate limiting
 * @param {Object} event - Netlify event
 * @returns {string} Client identifier
 */
function getClientIdentifier(event) {
    // Use IP address as primary identifier
    const ip = event.headers['x-forwarded-for'] || 
               event.headers['x-real-ip'] || 
               event.headers['client-ip'] ||
               'unknown';
    
    // For authenticated users, could use user ID
    const authHeader = event.headers.authorization || event.headers.Authorization;
    if (authHeader) {
        // Extract user ID from token (simplified)
        return `user_${authHeader.substring(0, 10)}`;
    }
    
    return `ip_${ip}`;
}

/**
 * Log usage for analytics
 * @param {Object} usageData - Usage data
 * @returns {Promise<void>}
 */
async function logUsage(usageData) {
    try {
        // In production, send to analytics service (e.g., Google Analytics, Mixpanel)
        console.log('Usage logged:', {
            userTier: usageData.userTier,
            textLength: usageData.textLength,
            processingTime: usageData.processingTime,
            success: usageData.success,
            timestamp: usageData.timestamp
        });
    } catch (error) {
        console.error('Failed to log usage:', error);
        // Don't throw error - logging failure shouldn't break the main function
    }
}

/**
 * Generate unique error ID for tracking
 * @returns {string} Error ID
 */
function generateErrorId() {
    return `err_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
}

/**
 * Health check endpoint
 * @param {Object} event - Netlify event
 * @returns {Object} Health status
 */
export const healthCheck = async (event) => {
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Check service status
        const serviceStatus = humanizationService.getServiceStatus();
        
        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: '2.0.0',
                environment: 'netlify',
                service: serviceStatus
            })
        };
    } catch (error) {
        return {
            statusCode: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date().toISOString()
            })
        };
    }
};
