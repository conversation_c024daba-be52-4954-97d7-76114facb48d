/**
 * Netlify Deployment Test Suite
 * 
 * Tests Netlify functions and serverless deployment compatibility.
 * Validates ES modules, function endpoints, and production readiness.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import fetch from 'node-fetch';

// Configuration
const NETLIFY_URL = process.env.NETLIFY_URL || 'http://localhost:8888';
const TEST_TIMEOUT = 30000; // 30 seconds

// Test data
const testTexts = [
    {
        name: "Short AI Text",
        text: "Furthermore, artificial intelligence demonstrates significant potential for revolutionizing industries.",
        expectedImprovement: true
    },
    {
        name: "Medium AI Text", 
        text: "Additionally, these sophisticated systems utilize advanced algorithms to effectively analyze vast amounts of data. Moreover, the comprehensive implementation of machine learning technologies has resulted in substantial improvements across various sectors.",
        expectedImprovement: true
    },
    {
        name: "Batch Test Data",
        texts: [
            "The system demonstrates remarkable performance in various scenarios.",
            "Furthermore, the implementation shows significant improvements.",
            "Additionally, the results indicate substantial progress."
        ]
    }
];

/**
 * Test Netlify function endpoint
 */
async function testNetlifyFunction(endpoint, payload, expectedStatus = 200) {
    const url = `${NETLIFY_URL}/.netlify/functions/${endpoint}`;
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
            timeout: TEST_TIMEOUT
        });

        const data = await response.json();
        
        return {
            success: response.status === expectedStatus,
            status: response.status,
            data,
            url
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            url
        };
    }
}

/**
 * Test health check endpoint
 */
async function testHealthCheck() {
    const url = `${NETLIFY_URL}/.netlify/functions/humanize`;
    
    try {
        const response = await fetch(url, {
            method: 'GET',
            timeout: TEST_TIMEOUT
        });

        return {
            success: response.status === 200 || response.status === 405, // 405 is expected for GET on POST endpoint
            status: response.status,
            url
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            url
        };
    }
}

/**
 * Main test runner
 */
async function runNetlifyTests() {
    console.log('🌐 Netlify Deployment Test Suite');
    console.log('='.repeat(50));
    console.log(`🔗 Testing URL: ${NETLIFY_URL}`);
    console.log(`⏱️  Timeout: ${TEST_TIMEOUT}ms`);
    console.log(`📅 Test Date: ${new Date().toISOString()}`);
    console.log('='.repeat(50));

    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;

    // Test 1: Health Check
    console.log('\n📋 Test 1: Health Check');
    console.log('-'.repeat(30));
    
    try {
        const healthResult = await testHealthCheck();
        
        if (healthResult.success) {
            console.log('✅ Health check passed');
            console.log(`   - Status: ${healthResult.status}`);
            console.log(`   - URL: ${healthResult.url}`);
            passedTests++;
        } else {
            console.log('❌ Health check failed');
            console.log(`   - Error: ${healthResult.error}`);
            console.log(`   - Status: ${healthResult.status}`);
            failedTests++;
        }
        totalTests++;
    } catch (error) {
        console.log('❌ Health check error:', error.message);
        failedTests++;
        totalTests++;
    }

    // Test 2: Humanization Function
    console.log('\n📋 Test 2: Humanization Function');
    console.log('-'.repeat(30));
    
    for (const testCase of testTexts.slice(0, 2)) { // Skip batch test for now
        try {
            const result = await testNetlifyFunction('humanize', {
                text: testCase.text,
                options: {
                    userTier: 'premium',
                    targetDetection: 10,
                    aggressiveness: 0.8
                }
            });
            
            if (result.success && result.data.success) {
                console.log(`✅ Humanization successful: ${testCase.name}`);
                console.log(`   - Original Length: ${testCase.text.length}`);
                console.log(`   - Humanized Length: ${result.data.humanizedText?.length || 0}`);
                console.log(`   - Processing Time: ${result.data.processingTime}ms`);
                console.log(`   - AI Detection: ${result.data.qualityMetrics?.aiDetectionScore || 'N/A'}%`);
                console.log(`   - Transformation Rate: ${result.data.transformationStats?.transformationRate || 'N/A'}%`);
                passedTests++;
            } else {
                console.log(`❌ Humanization failed: ${testCase.name}`);
                console.log(`   - Status: ${result.status}`);
                console.log(`   - Error: ${result.data?.error || result.error}`);
                failedTests++;
            }
            totalTests++;
        } catch (error) {
            console.log(`❌ Humanization error for ${testCase.name}:`, error.message);
            failedTests++;
            totalTests++;
        }
    }

    // Test 3: Analysis Function
    console.log('\n📋 Test 3: Analysis Function');
    console.log('-'.repeat(30));
    
    try {
        const result = await testNetlifyFunction('analyze', {
            text: testTexts[0].text,
            options: {
                type: 'comprehensive',
                targetDetection: 10
            }
        });
        
        if (result.success && result.data.success) {
            console.log('✅ Analysis function successful');
            console.log(`   - Analysis Type: ${result.data.analysisType}`);
            console.log(`   - Overall Score: ${result.data.overallScore || 'N/A'}`);
            console.log(`   - Quality Level: ${result.data.qualityLevel || 'N/A'}`);
            console.log(`   - AI Detection: ${result.data.aiDetection?.score || 'N/A'}%`);
            console.log(`   - Processing Time: ${result.data.metadata?.processingTime || 'N/A'}ms`);
            passedTests++;
        } else {
            console.log('❌ Analysis function failed');
            console.log(`   - Status: ${result.status}`);
            console.log(`   - Error: ${result.data?.error || result.error}`);
            failedTests++;
        }
        totalTests++;
    } catch (error) {
        console.log('❌ Analysis function error:', error.message);
        failedTests++;
        totalTests++;
    }

    // Test 4: Batch Processing Function
    console.log('\n📋 Test 4: Batch Processing Function');
    console.log('-'.repeat(30));
    
    try {
        const batchTestCase = testTexts[2];
        const result = await testNetlifyFunction('batch-humanize', {
            texts: batchTestCase.texts,
            options: {
                userTier: 'premium',
                targetDetection: 10
            }
        });
        
        if (result.success && result.data.success) {
            console.log('✅ Batch processing successful');
            console.log(`   - Batch Size: ${result.data.batchSize}`);
            console.log(`   - Success Count: ${result.data.statistics?.successCount || 'N/A'}`);
            console.log(`   - Success Rate: ${result.data.statistics?.successRate || 'N/A'}%`);
            console.log(`   - Average Processing Time: ${result.data.statistics?.averageProcessingTime || 'N/A'}ms`);
            console.log(`   - Average AI Detection: ${result.data.statistics?.averageAIDetection || 'N/A'}%`);
            passedTests++;
        } else {
            console.log('❌ Batch processing failed');
            console.log(`   - Status: ${result.status}`);
            console.log(`   - Error: ${result.data?.error || result.error}`);
            failedTests++;
        }
        totalTests++;
    } catch (error) {
        console.log('❌ Batch processing error:', error.message);
        failedTests++;
        totalTests++;
    }

    // Test 5: Error Handling
    console.log('\n📋 Test 5: Error Handling');
    console.log('-'.repeat(30));
    
    const errorTests = [
        {
            name: 'Empty Text',
            endpoint: 'humanize',
            payload: { text: '' },
            expectedStatus: 400
        },
        {
            name: 'Invalid JSON',
            endpoint: 'humanize',
            payload: 'invalid json',
            expectedStatus: 400
        },
        {
            name: 'Missing Text Field',
            endpoint: 'analyze',
            payload: { options: {} },
            expectedStatus: 400
        }
    ];
    
    for (const errorTest of errorTests) {
        try {
            const result = await testNetlifyFunction(
                errorTest.endpoint, 
                errorTest.payload, 
                errorTest.expectedStatus
            );
            
            if (result.success) {
                console.log(`✅ Error handling correct: ${errorTest.name}`);
                console.log(`   - Expected Status: ${errorTest.expectedStatus}`);
                console.log(`   - Actual Status: ${result.status}`);
                passedTests++;
            } else {
                console.log(`❌ Error handling incorrect: ${errorTest.name}`);
                console.log(`   - Expected Status: ${errorTest.expectedStatus}`);
                console.log(`   - Actual Status: ${result.status}`);
                failedTests++;
            }
            totalTests++;
        } catch (error) {
            console.log(`❌ Error test failed for ${errorTest.name}:`, error.message);
            failedTests++;
            totalTests++;
        }
    }

    // Test 6: Performance Benchmarking
    console.log('\n📋 Test 6: Performance Benchmarking');
    console.log('-'.repeat(30));
    
    const performanceTests = [
        { name: 'Small Text (100 chars)', text: 'A'.repeat(100) },
        { name: 'Medium Text (500 chars)', text: 'Furthermore, artificial intelligence demonstrates significant potential. '.repeat(7) },
        { name: 'Large Text (2000 chars)', text: 'Additionally, these sophisticated systems utilize advanced algorithms to effectively analyze vast amounts of data. '.repeat(18) }
    ];
    
    for (const perfTest of performanceTests) {
        try {
            const startTime = Date.now();
            const result = await testNetlifyFunction('humanize', {
                text: perfTest.text,
                options: { userTier: 'premium' }
            });
            const endTime = Date.now();
            
            const totalTime = endTime - startTime;
            const processingTime = result.data?.processingTime || 0;
            const networkTime = totalTime - processingTime;
            
            if (result.success) {
                console.log(`✅ Performance test passed: ${perfTest.name}`);
                console.log(`   - Total Time: ${totalTime}ms`);
                console.log(`   - Processing Time: ${processingTime}ms`);
                console.log(`   - Network Time: ${networkTime}ms`);
                console.log(`   - Text Length: ${perfTest.text.length} chars`);
                passedTests++;
            } else {
                console.log(`❌ Performance test failed: ${perfTest.name}`);
                console.log(`   - Error: ${result.data?.error || result.error}`);
                failedTests++;
            }
            totalTests++;
        } catch (error) {
            console.log(`❌ Performance test error for ${perfTest.name}:`, error.message);
            failedTests++;
            totalTests++;
        }
    }

    // Final Results
    console.log('\n' + '='.repeat(50));
    console.log('📊 NETLIFY DEPLOYMENT TEST RESULTS');
    console.log('='.repeat(50));
    console.log(`✅ Passed Tests: ${passedTests}/${totalTests}`);
    console.log(`❌ Failed Tests: ${failedTests}/${totalTests}`);
    console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (failedTests === 0) {
        console.log('\n🎉 ALL NETLIFY TESTS PASSED! Deployment is ready.');
        console.log('\n✅ Deployment Checklist:');
        console.log('   ✅ Functions are accessible');
        console.log('   ✅ ES modules working correctly');
        console.log('   ✅ Error handling implemented');
        console.log('   ✅ Performance within acceptable limits');
    } else if (failedTests < totalTests * 0.2) {
        console.log('\n⚠️  Minor deployment issues detected.');
        console.log('   - Review failed tests');
        console.log('   - Check environment variables');
        console.log('   - Verify function configurations');
    } else {
        console.log('\n🚨 Significant deployment issues detected!');
        console.log('   - Fix failed tests before going live');
        console.log('   - Check Netlify function logs');
        console.log('   - Verify build configuration');
    }
    
    console.log('\n🔗 Useful Commands:');
    console.log('   - Local dev: netlify dev');
    console.log('   - Deploy: netlify deploy --prod');
    console.log('   - Logs: netlify functions:log');
    console.log('   - Status: netlify status');
    
    return {
        totalTests,
        passedTests,
        failedTests,
        successRate: Math.round((passedTests / totalTests) * 100),
        deploymentReady: failedTests === 0
    };
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runNetlifyTests().catch(console.error);
}

export { runNetlifyTests };
