/**
 * Quality Validator
 * 
 * Comprehensive quality assessment system for humanized text.
 * Validates AI detection scores, grammar quality, readability,
 * and tone consistency to ensure professional output.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

/**
 * Quality Validator class for comprehensive text quality assessment
 */
export class QualityValidator {
    constructor() {
        this.initializeValidationRules();
        this.initializeQualityThresholds();
        this.initializeGrammarRules();
    }

    /**
     * Initialize validation rules and criteria
     */
    initializeValidationRules() {
        // AI detection patterns to check for
        this.aiDetectionPatterns = {
            highRisk: {
                patterns: [
                    /\b(furthermore|moreover|additionally|consequently|therefore)\b/gi,
                    /\b(comprehensive|sophisticated|cutting-edge|state-of-the-art)\b/gi,
                    /\b(utilize|implement|demonstrate|optimize|leverage)\b/gi,
                    /\b(delve|realm|landscape|paradigm|framework)\b/gi
                ],
                weight: 0.8,
                description: 'High-risk AI detection triggers'
            },
            
            mediumRisk: {
                patterns: [
                    /\b(significant|substantial|considerable|remarkable|exceptional)\b/gi,
                    /\b(however|nevertheless|nonetheless|meanwhile|subsequently)\b/gi,
                    /\b(analyze|evaluate|assess|examine|investigate)\b/gi
                ],
                weight: 0.5,
                description: 'Medium-risk AI patterns'
            },
            
            structuralRisk: {
                patterns: [
                    /^(The|This|These|Those)\s+\w+\s+(is|are|was|were)/gm,
                    /^(It|They)\s+(is|are|was|were)\s+(important|significant|crucial)/gm,
                    /\b(can be|may be|might be)\s+\w+ed\b/gi
                ],
                weight: 0.6,
                description: 'Repetitive structural patterns'
            }
        };

        // Quality indicators
        this.qualityIndicators = {
            naturalness: {
                contractions: /\b\w+'(t|re|ve|ll|d|s)\b/g,
                personalPronouns: /\b(I|you|we|they|me|us|them)\b/gi,
                casualPhrases: /\b(you know|I mean|sort of|kind of|basically)\b/gi,
                questions: /\?/g
            },
            
            diversity: {
                uniqueWords: true, // Calculated separately
                sentenceVariation: true, // Calculated separately
                vocabularyRange: true // Calculated separately
            },
            
            readability: {
                averageSentenceLength: { min: 12, max: 25 },
                averageWordLength: { min: 4, max: 6 },
                syllableComplexity: { min: 1.2, max: 1.8 }
            }
        };
    }

    /**
     * Initialize quality thresholds for different metrics
     */
    initializeQualityThresholds() {
        this.qualityThresholds = {
            // AI detection score thresholds
            aiDetection: {
                excellent: 5,    // ≤5% detection
                good: 10,        // ≤10% detection
                acceptable: 20,  // ≤20% detection
                poor: 50         // >50% detection
            },
            
            // Grammar quality thresholds
            grammar: {
                excellent: 95,   // ≥95% grammar score
                good: 85,        // ≥85% grammar score
                acceptable: 75,  // ≥75% grammar score
                poor: 60         // <60% grammar score
            },
            
            // Readability thresholds (Flesch Reading Ease)
            readability: {
                excellent: { min: 60, max: 80 },  // Standard readability
                good: { min: 50, max: 90 },       // Acceptable range
                acceptable: { min: 30, max: 100 }, // Wide range
                poor: { min: 0, max: 30 }         // Too difficult
            },
            
            // Tone consistency thresholds
            toneConsistency: {
                excellent: 90,   // ≥90% consistency
                good: 80,        // ≥80% consistency
                acceptable: 70,  // ≥70% consistency
                poor: 50         // <50% consistency
            }
        };
    }

    /**
     * Initialize basic grammar checking rules
     */
    initializeGrammarRules() {
        this.grammarRules = {
            // Common grammar issues
            commonErrors: [
                {
                    pattern: /\b(there|their|they're)\b/gi,
                    check: 'there/their/they\'re usage',
                    weight: 0.3
                },
                {
                    pattern: /\b(its|it's)\b/gi,
                    check: 'its/it\'s usage',
                    weight: 0.3
                },
                {
                    pattern: /\b(your|you're)\b/gi,
                    check: 'your/you\'re usage',
                    weight: 0.3
                },
                {
                    pattern: /\b(then|than)\b/gi,
                    check: 'then/than usage',
                    weight: 0.2
                }
            ],
            
            // Punctuation rules
            punctuation: [
                {
                    pattern: /\s+[.,;:!?]/g,
                    issue: 'Space before punctuation',
                    weight: 0.2
                },
                {
                    pattern: /[.,;:!?]\w/g,
                    issue: 'Missing space after punctuation',
                    weight: 0.2
                },
                {
                    pattern: /[.!?]{2,}/g,
                    issue: 'Multiple punctuation marks',
                    weight: 0.1
                }
            ],
            
            // Capitalization rules
            capitalization: [
                {
                    pattern: /\.\s+[a-z]/g,
                    issue: 'Sentence should start with capital letter',
                    weight: 0.4
                },
                {
                    pattern: /\b[A-Z]{2,}\b/g,
                    issue: 'Excessive capitalization',
                    weight: 0.1
                }
            ]
        };
    }

    /**
     * Validate text quality comprehensively
     * @param {string} text - Text to validate
     * @param {number} targetDetection - Target AI detection threshold
     * @returns {Promise<Object>} Validation results
     */
    async validateQuality(text, targetDetection = 10) {
        const startTime = Date.now();

        try {
            // Perform all quality checks
            const aiDetectionScore = await this.assessAIDetection(text);
            const grammarScore = await this.assessGrammar(text);
            const readabilityScore = await this.assessReadability(text);
            const toneConsistency = await this.assessToneConsistency(text);

            // Calculate overall quality score
            const overallScore = this.calculateOverallScore({
                aiDetectionScore,
                grammarScore,
                readabilityScore,
                toneConsistency
            });

            // Determine if quality meets target
            const meetsTarget = aiDetectionScore <= targetDetection;
            const qualityLevel = this.determineQualityLevel(overallScore);

            // Generate recommendations
            const recommendations = this.generateQualityRecommendations({
                aiDetectionScore,
                grammarScore,
                readabilityScore,
                toneConsistency,
                targetDetection
            });

            return {
                success: true,
                processingTime: Date.now() - startTime,
                aiDetectionScore,
                grammarScore,
                readabilityScore,
                toneConsistency,
                overallScore,
                qualityLevel,
                meetsTarget,
                targetDetection,
                recommendations,
                details: {
                    textLength: text.length,
                    wordCount: this.countWords(text),
                    sentenceCount: this.countSentences(text)
                }
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                processingTime: Date.now() - startTime
            };
        }
    }

    /**
     * Assess AI detection risk
     * @param {string} text - Text to assess
     * @returns {Promise<number>} AI detection score (0-100)
     */
    async assessAIDetection(text) {
        let totalScore = 0;
        let totalWeight = 0;

        // Check each pattern category
        for (const [category, data] of Object.entries(this.aiDetectionPatterns)) {
            let categoryMatches = 0;
            
            for (const pattern of data.patterns) {
                const matches = (text.match(pattern) || []).length;
                categoryMatches += matches;
            }

            if (categoryMatches > 0) {
                const categoryScore = Math.min(categoryMatches * 10, 100);
                totalScore += categoryScore * data.weight;
                totalWeight += data.weight;
            }
        }

        // Normalize score
        const normalizedScore = totalWeight > 0 ? totalScore / totalWeight : 0;
        
        // Apply text length adjustment
        const wordCount = this.countWords(text);
        const lengthAdjustment = Math.min(wordCount / 100, 1); // Longer texts may have more patterns
        
        return Math.min(Math.round(normalizedScore * lengthAdjustment), 100);
    }

    /**
     * Assess grammar quality
     * @param {string} text - Text to assess
     * @returns {Promise<number>} Grammar score (0-100)
     */
    async assessGrammar(text) {
        let errorCount = 0;
        let totalChecks = 0;

        // Check common grammar errors
        for (const rule of this.grammarRules.commonErrors) {
            const matches = (text.match(rule.pattern) || []).length;
            // This is a simplified check - in reality, would need context analysis
            totalChecks += matches;
        }

        // Check punctuation issues
        for (const rule of this.grammarRules.punctuation) {
            const matches = (text.match(rule.pattern) || []).length;
            errorCount += matches * rule.weight;
            totalChecks += matches;
        }

        // Check capitalization issues
        for (const rule of this.grammarRules.capitalization) {
            const matches = (text.match(rule.pattern) || []).length;
            errorCount += matches * rule.weight;
            totalChecks += matches;
        }

        // Calculate grammar score
        const wordCount = this.countWords(text);
        const errorRate = errorCount / Math.max(wordCount / 100, 1); // Errors per 100 words
        const grammarScore = Math.max(0, 100 - (errorRate * 10));

        return Math.round(grammarScore);
    }

    /**
     * Assess readability
     * @param {string} text - Text to assess
     * @returns {Promise<number>} Readability score (0-100)
     */
    async assessReadability(text) {
        const sentences = this.countSentences(text);
        const words = this.countWords(text);
        const syllables = this.countSyllables(text);

        if (sentences === 0 || words === 0) return 0;

        // Calculate Flesch Reading Ease Score
        const avgSentenceLength = words / sentences;
        const avgSyllablesPerWord = syllables / words;
        
        const fleschScore = 206.835 - 
            (1.015 * avgSentenceLength) - 
            (84.6 * avgSyllablesPerWord);

        // Convert to 0-100 scale where higher is better
        const normalizedScore = Math.max(0, Math.min(100, fleschScore));
        
        return Math.round(normalizedScore);
    }

    /**
     * Assess tone consistency
     * @param {string} text - Text to assess
     * @returns {Promise<number>} Tone consistency score (0-100)
     */
    async assessToneConsistency(text) {
        // Analyze different tone indicators
        const indicators = this.qualityIndicators.naturalness;
        
        const contractions = (text.match(indicators.contractions) || []).length;
        const personalPronouns = (text.match(indicators.personalPronouns) || []).length;
        const casualPhrases = (text.match(indicators.casualPhrases) || []).length;
        const questions = (text.match(indicators.questions) || []).length;

        const wordCount = this.countWords(text);
        const sentenceCount = this.countSentences(text);

        // Calculate tone indicators
        const contractionRatio = contractions / wordCount;
        const personalPronounRatio = personalPronouns / wordCount;
        const casualPhraseRatio = casualPhrases / sentenceCount;
        const questionRatio = questions / sentenceCount;

        // Assess consistency (simplified approach)
        // In a real implementation, this would be more sophisticated
        const toneScores = [
            this.scoreInRange(contractionRatio, 0.05, 0.15) * 100,
            this.scoreInRange(personalPronounRatio, 0.02, 0.08) * 100,
            this.scoreInRange(casualPhraseRatio, 0.0, 0.1) * 100,
            this.scoreInRange(questionRatio, 0.0, 0.1) * 100
        ];

        const averageScore = toneScores.reduce((sum, score) => sum + score, 0) / toneScores.length;
        
        return Math.round(averageScore);
    }

    /**
     * Calculate overall quality score
     * @param {Object} scores - Individual quality scores
     * @returns {number} Overall quality score (0-100)
     */
    calculateOverallScore(scores) {
        const weights = {
            aiDetectionScore: 0.4,  // AI detection is most important
            grammarScore: 0.3,      // Grammar is important for professionalism
            readabilityScore: 0.2,  // Readability affects user experience
            toneConsistency: 0.1    // Tone consistency is nice to have
        };

        // Invert AI detection score (lower is better)
        const invertedAIScore = 100 - scores.aiDetectionScore;

        const weightedScore = 
            (invertedAIScore * weights.aiDetectionScore) +
            (scores.grammarScore * weights.grammarScore) +
            (scores.readabilityScore * weights.readabilityScore) +
            (scores.toneConsistency * weights.toneConsistency);

        return Math.round(weightedScore);
    }

    /**
     * Determine quality level based on overall score
     * @param {number} overallScore - Overall quality score
     * @returns {string} Quality level
     */
    determineQualityLevel(overallScore) {
        if (overallScore >= 90) return 'Excellent';
        if (overallScore >= 80) return 'Good';
        if (overallScore >= 70) return 'Acceptable';
        if (overallScore >= 60) return 'Fair';
        return 'Poor';
    }

    /**
     * Generate quality improvement recommendations
     * @param {Object} scores - Quality scores
     * @returns {Array} Array of recommendations
     */
    generateQualityRecommendations(scores) {
        const recommendations = [];

        // AI detection recommendations
        if (scores.aiDetectionScore > scores.targetDetection) {
            recommendations.push({
                type: 'ai-detection',
                priority: 'high',
                message: `AI detection score (${scores.aiDetectionScore}%) exceeds target (${scores.targetDetection}%). Consider more aggressive humanization.`,
                suggestions: [
                    'Replace formal transition words',
                    'Vary sentence structures',
                    'Add more natural language elements'
                ]
            });
        }

        // Grammar recommendations
        if (scores.grammarScore < 80) {
            recommendations.push({
                type: 'grammar',
                priority: 'medium',
                message: `Grammar score (${scores.grammarScore}%) could be improved.`,
                suggestions: [
                    'Review punctuation usage',
                    'Check capitalization',
                    'Verify word usage'
                ]
            });
        }

        // Readability recommendations
        if (scores.readabilityScore < 60) {
            recommendations.push({
                type: 'readability',
                priority: 'medium',
                message: `Readability score (${scores.readabilityScore}%) indicates text may be difficult to read.`,
                suggestions: [
                    'Shorten complex sentences',
                    'Use simpler vocabulary',
                    'Improve paragraph structure'
                ]
            });
        }

        // Tone consistency recommendations
        if (scores.toneConsistency < 70) {
            recommendations.push({
                type: 'tone',
                priority: 'low',
                message: `Tone consistency (${scores.toneConsistency}%) could be more uniform.`,
                suggestions: [
                    'Maintain consistent formality level',
                    'Balance personal and impersonal language',
                    'Ensure appropriate use of contractions'
                ]
            });
        }

        return recommendations;
    }

    /**
     * Count words in text
     * @param {string} text - Input text
     * @returns {number} Word count
     */
    countWords(text) {
        return text.split(/\s+/).filter(word => word.trim().length > 0).length;
    }

    /**
     * Count sentences in text
     * @param {string} text - Input text
     * @returns {number} Sentence count
     */
    countSentences(text) {
        return text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0).length;
    }

    /**
     * Count syllables in text (approximation)
     * @param {string} text - Input text
     * @returns {number} Syllable count
     */
    countSyllables(text) {
        const words = text.split(/\s+/).filter(word => word.trim().length > 0);
        return words.reduce((total, word) => total + this.countWordSyllables(word), 0);
    }

    /**
     * Count syllables in a single word
     * @param {string} word - Word to analyze
     * @returns {number} Syllable count
     */
    countWordSyllables(word) {
        word = word.toLowerCase().replace(/[^a-z]/g, '');
        if (word.length <= 3) return 1;
        
        const vowels = 'aeiouy';
        let count = 0;
        let previousWasVowel = false;
        
        for (let i = 0; i < word.length; i++) {
            const isVowel = vowels.includes(word[i]);
            if (isVowel && !previousWasVowel) {
                count++;
            }
            previousWasVowel = isVowel;
        }
        
        // Adjust for silent 'e'
        if (word.endsWith('e')) count--;
        
        return Math.max(1, count);
    }

    /**
     * Score a value within an ideal range
     * @param {number} value - Value to score
     * @param {number} min - Minimum ideal value
     * @param {number} max - Maximum ideal value
     * @returns {number} Score (0-1)
     */
    scoreInRange(value, min, max) {
        if (value >= min && value <= max) return 1;
        if (value < min) return Math.max(0, value / min);
        return Math.max(0, max / value);
    }

    /**
     * Get validation statistics
     * @returns {Object} Statistics
     */
    getStatistics() {
        return {
            aiPatternCategories: Object.keys(this.aiDetectionPatterns).length,
            totalAIPatterns: Object.values(this.aiDetectionPatterns)
                .reduce((sum, category) => sum + category.patterns.length, 0),
            grammarRuleCategories: Object.keys(this.grammarRules).length,
            qualityThresholds: Object.keys(this.qualityThresholds).length,
            version: '2.0.0'
        };
    }
}

// Export singleton instance
export const qualityValidator = new QualityValidator();
