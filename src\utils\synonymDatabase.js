/**
 * Advanced Synonym Database
 * 
 * Comprehensive synonym replacement system optimized for AI detection avoidance.
 * Contains carefully curated synonyms for common AI-detectable words and phrases.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

/**
 * Synonym Database class for intelligent word replacement
 */
export class SynonymDatabase {
    constructor() {
        this.initializeSynonymMaps();
        this.initializeContextualRules();
        this.initializeAIDetectionTriggers();
    }

    /**
     * Initialize comprehensive synonym mappings
     */
    initializeSynonymMaps() {
        // Adjectives - targeting common AI-detectable words
        this.adjectives = {
            // Quality descriptors
            'good': ['excellent', 'great', 'fine', 'wonderful', 'superb', 'outstanding', 'remarkable', 'impressive', 'solid', 'decent'],
            'bad': ['terrible', 'awful', 'horrible', 'dreadful', 'poor', 'disappointing', 'inadequate', 'subpar', 'inferior', 'unsatisfactory'],
            'great': ['excellent', 'outstanding', 'remarkable', 'superb', 'fantastic', 'wonderful', 'impressive', 'exceptional', 'magnificent', 'splendid'],
            'excellent': ['outstanding', 'superb', 'exceptional', 'remarkable', 'magnificent', 'splendid', 'first-rate', 'top-notch', 'superior', 'exemplary'],
            
            // Size descriptors
            'big': ['large', 'huge', 'enormous', 'massive', 'substantial', 'considerable', 'significant', 'extensive', 'vast', 'immense'],
            'small': ['tiny', 'little', 'minute', 'compact', 'modest', 'limited', 'minor', 'minimal', 'slight', 'petite'],
            'large': ['big', 'huge', 'enormous', 'massive', 'substantial', 'extensive', 'considerable', 'significant', 'vast', 'immense'],
            
            // Importance descriptors
            'important': ['significant', 'crucial', 'vital', 'essential', 'critical', 'key', 'major', 'fundamental', 'pivotal', 'paramount'],
            'significant': ['important', 'substantial', 'considerable', 'notable', 'meaningful', 'major', 'crucial', 'vital', 'key', 'relevant'],
            'major': ['significant', 'important', 'substantial', 'considerable', 'key', 'primary', 'principal', 'main', 'chief', 'leading'],
            'minor': ['small', 'slight', 'modest', 'limited', 'negligible', 'trivial', 'insignificant', 'marginal', 'secondary', 'lesser'],
            
            // Temporal descriptors
            'new': ['recent', 'fresh', 'modern', 'current', 'latest', 'contemporary', 'up-to-date', 'novel', 'innovative', 'cutting-edge'],
            'old': ['ancient', 'aged', 'vintage', 'traditional', 'established', 'mature', 'seasoned', 'time-tested', 'classic', 'historical'],
            'recent': ['new', 'fresh', 'latest', 'current', 'modern', 'contemporary', 'up-to-date', 'present-day', 'just-released', 'brand-new'],
            
            // Quantity descriptors
            'many': ['numerous', 'countless', 'multiple', 'various', 'several', 'abundant', 'plenty', 'loads', 'tons', 'heaps'],
            'few': ['several', 'some', 'a handful', 'limited', 'scarce', 'minimal', 'sparse', 'rare', 'occasional', 'infrequent'],
            'various': ['different', 'diverse', 'multiple', 'numerous', 'assorted', 'mixed', 'varied', 'several', 'distinct', 'separate'],
            
            // AI-specific triggers
            'artificial': ['synthetic', 'simulated', 'manufactured', 'engineered', 'constructed', 'fabricated', 'man-made', 'created', 'produced', 'generated'],
            'advanced': ['sophisticated', 'cutting-edge', 'state-of-the-art', 'innovative', 'progressive', 'modern', 'developed', 'refined', 'enhanced', 'improved'],
            'sophisticated': ['advanced', 'complex', 'refined', 'elaborate', 'intricate', 'nuanced', 'developed', 'polished', 'cultured', 'elegant'],
            'comprehensive': ['complete', 'thorough', 'extensive', 'detailed', 'full', 'exhaustive', 'all-encompassing', 'wide-ranging', 'broad', 'inclusive']
        };

        // Adverbs - targeting common AI-detectable modifiers
        this.adverbs = {
            // Intensity modifiers
            'very': ['extremely', 'incredibly', 'remarkably', 'exceptionally', 'particularly', 'especially', 'quite', 'rather', 'highly', 'tremendously'],
            'extremely': ['incredibly', 'remarkably', 'exceptionally', 'extraordinarily', 'immensely', 'tremendously', 'vastly', 'hugely', 'massively', 'intensely'],
            'really': ['truly', 'genuinely', 'actually', 'indeed', 'certainly', 'definitely', 'absolutely', 'positively', 'undoubtedly', 'unquestionably'],
            'quite': ['rather', 'fairly', 'somewhat', 'pretty', 'reasonably', 'moderately', 'relatively', 'considerably', 'notably', 'substantially'],
            
            // Certainty modifiers
            'clearly': ['obviously', 'evidently', 'apparently', 'plainly', 'undoubtedly', 'certainly', 'definitely', 'unmistakably', 'distinctly', 'manifestly'],
            'obviously': ['clearly', 'evidently', 'apparently', 'plainly', 'undoubtedly', 'certainly', 'definitely', 'unmistakably', 'naturally', 'of course'],
            'certainly': ['definitely', 'absolutely', 'positively', 'undoubtedly', 'unquestionably', 'surely', 'indeed', 'without doubt', 'clearly', 'obviously'],
            'definitely': ['certainly', 'absolutely', 'positively', 'undoubtedly', 'unquestionably', 'surely', 'indeed', 'without doubt', 'clearly', 'decidedly'],
            
            // Frequency modifiers
            'often': ['frequently', 'regularly', 'commonly', 'typically', 'usually', 'generally', 'routinely', 'habitually', 'repeatedly', 'consistently'],
            'always': ['constantly', 'continually', 'perpetually', 'invariably', 'consistently', 'regularly', 'repeatedly', 'unfailingly', 'without exception', 'every time'],
            'never': ['not ever', 'at no time', 'under no circumstances', 'not once', 'not at all', 'by no means', 'absolutely not', 'certainly not', 'definitely not', 'on no occasion'],
            
            // Sequence modifiers
            'furthermore': ['moreover', 'additionally', 'besides', 'also', 'in addition', 'what is more', 'on top of that', 'beyond that', 'apart from that', 'not to mention'],
            'moreover': ['furthermore', 'additionally', 'besides', 'also', 'in addition', 'what is more', 'on top of that', 'beyond that', 'apart from that', 'likewise'],
            'additionally': ['furthermore', 'moreover', 'besides', 'also', 'in addition', 'what is more', 'on top of that', 'beyond that', 'apart from that', 'as well'],
            'however': ['nevertheless', 'nonetheless', 'yet', 'still', 'though', 'although', 'but', 'on the other hand', 'conversely', 'in contrast'],
            'therefore': ['consequently', 'thus', 'hence', 'as a result', 'accordingly', 'for this reason', 'because of this', 'due to this', 'so', 'thereby']
        };

        // Verbs - common action words that trigger AI detection
        this.verbs = {
            'demonstrate': ['show', 'illustrate', 'display', 'exhibit', 'reveal', 'present', 'indicate', 'prove', 'establish', 'manifest'],
            'utilize': ['use', 'employ', 'apply', 'implement', 'deploy', 'leverage', 'harness', 'exploit', 'make use of', 'take advantage of'],
            'implement': ['execute', 'carry out', 'put into practice', 'apply', 'deploy', 'establish', 'install', 'introduce', 'set up', 'activate'],
            'analyze': ['examine', 'study', 'investigate', 'assess', 'evaluate', 'review', 'scrutinize', 'inspect', 'dissect', 'break down'],
            'optimize': ['improve', 'enhance', 'refine', 'perfect', 'streamline', 'fine-tune', 'upgrade', 'boost', 'maximize', 'polish']
        };
    }

    /**
     * Initialize contextual replacement rules
     */
    initializeContextualRules() {
        this.contextualRules = {
            // Formal vs informal contexts
            formal: {
                'good': ['excellent', 'outstanding', 'superior', 'exemplary'],
                'bad': ['inadequate', 'substandard', 'unsatisfactory', 'deficient'],
                'very': ['extremely', 'exceptionally', 'remarkably', 'particularly']
            },
            
            informal: {
                'good': ['great', 'awesome', 'fantastic', 'solid'],
                'bad': ['terrible', 'awful', 'horrible', 'lousy'],
                'very': ['really', 'super', 'pretty', 'quite']
            },
            
            // Technical vs non-technical contexts
            technical: {
                'use': ['utilize', 'employ', 'implement', 'deploy'],
                'show': ['demonstrate', 'illustrate', 'exhibit', 'display'],
                'improve': ['optimize', 'enhance', 'refine', 'streamline']
            }
        };
    }

    /**
     * Initialize AI detection trigger words
     */
    initializeAIDetectionTriggers() {
        // Words that commonly trigger AI detection
        this.aiTriggers = new Set([
            'furthermore', 'moreover', 'additionally', 'consequently', 'therefore',
            'comprehensive', 'sophisticated', 'advanced', 'innovative', 'cutting-edge',
            'utilize', 'implement', 'demonstrate', 'analyze', 'optimize',
            'significant', 'substantial', 'considerable', 'remarkable', 'exceptional',
            'artificial', 'intelligence', 'machine', 'learning', 'algorithm',
            'delve', 'realm', 'landscape', 'paradigm', 'framework'
        ]);
    }

    /**
     * Get best synonym for a word based on context
     * @param {string} word - Word to replace
     * @param {string} pos - Part of speech tag
     * @param {Object} context - Context information
     * @returns {Promise<string|null>} Best synonym or null
     */
    async getBestSynonym(word, pos, context) {
        const lowerWord = word.toLowerCase();
        
        // Get appropriate synonym map based on POS
        let synonymMap;
        if (pos.startsWith('JJ')) {
            synonymMap = this.adjectives;
        } else if (pos.startsWith('RB')) {
            synonymMap = this.adverbs;
        } else if (pos.startsWith('VB')) {
            synonymMap = this.verbs;
        } else {
            return null;
        }

        // Check if word has synonyms
        if (!synonymMap[lowerWord]) {
            return null;
        }

        const synonyms = synonymMap[lowerWord];
        
        // Apply contextual filtering
        const contextualSynonyms = this.filterSynonymsByContext(synonyms, context);
        
        // Prioritize AI trigger replacements
        if (this.aiTriggers.has(lowerWord)) {
            return this.selectBestSynonym(contextualSynonyms, 'ai-trigger');
        }
        
        // Select best synonym based on context
        return this.selectBestSynonym(contextualSynonyms, 'general');
    }

    /**
     * Filter synonyms based on context
     * @param {Array} synonyms - Available synonyms
     * @param {Object} context - Context information
     * @returns {Array} Filtered synonyms
     */
    filterSynonymsByContext(synonyms, context) {
        // For now, return all synonyms
        // In future versions, implement more sophisticated context filtering
        return synonyms;
    }

    /**
     * Select best synonym from available options
     * @param {Array} synonyms - Available synonyms
     * @param {string} priority - Selection priority
     * @returns {string} Selected synonym
     */
    selectBestSynonym(synonyms, priority) {
        if (!synonyms || synonyms.length === 0) {
            return null;
        }

        if (priority === 'ai-trigger') {
            // For AI triggers, prefer less common synonyms
            return synonyms[Math.floor(Math.random() * Math.min(3, synonyms.length))];
        }

        // For general replacement, use weighted random selection
        const weights = synonyms.map((_, index) => Math.max(1, synonyms.length - index));
        const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
        
        let random = Math.random() * totalWeight;
        for (let i = 0; i < synonyms.length; i++) {
            random -= weights[i];
            if (random <= 0) {
                return synonyms[i];
            }
        }
        
        return synonyms[0];
    }

    /**
     * Check if word is an AI detection trigger
     * @param {string} word - Word to check
     * @returns {boolean} Is AI trigger
     */
    isAITrigger(word) {
        return this.aiTriggers.has(word.toLowerCase());
    }

    /**
     * Get synonym statistics
     * @returns {Object} Database statistics
     */
    getStatistics() {
        return {
            totalAdjectives: Object.keys(this.adjectives).length,
            totalAdverbs: Object.keys(this.adverbs).length,
            totalVerbs: Object.keys(this.verbs).length,
            totalAITriggers: this.aiTriggers.size,
            averageSynonymsPerWord: {
                adjectives: Object.values(this.adjectives).reduce((sum, syns) => sum + syns.length, 0) / Object.keys(this.adjectives).length,
                adverbs: Object.values(this.adverbs).reduce((sum, syns) => sum + syns.length, 0) / Object.keys(this.adverbs).length,
                verbs: Object.values(this.verbs).reduce((sum, syns) => sum + syns.length, 0) / Object.keys(this.verbs).length
            }
        };
    }

    /**
     * Add custom synonym mapping
     * @param {string} word - Original word
     * @param {Array} synonyms - Synonym array
     * @param {string} pos - Part of speech
     */
    addCustomSynonym(word, synonyms, pos) {
        let targetMap;
        if (pos.startsWith('JJ')) {
            targetMap = this.adjectives;
        } else if (pos.startsWith('RB')) {
            targetMap = this.adverbs;
        } else if (pos.startsWith('VB')) {
            targetMap = this.verbs;
        } else {
            return false;
        }

        targetMap[word.toLowerCase()] = synonyms;
        return true;
    }
}

// Export singleton instance
export const synonymDatabase = new SynonymDatabase();
