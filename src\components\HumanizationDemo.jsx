/**
 * GhostLayer 2.0 Humanization Demo Component
 * 
 * Demonstrates the new humanization system with real-time processing,
 * quality metrics, and tier-based features.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import React, { useState, useCallback } from 'react';
import styles from './HumanizationDemo.module.css';

const HumanizationDemo = () => {
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStats, setProcessingStats] = useState(null);
  const [qualityMetrics, setQualityMetrics] = useState(null);
  const [error, setError] = useState(null);
  const [userTier, setUserTier] = useState('freemium');

  // Configuration options
  const [options, setOptions] = useState({
    targetDetection: 10,
    aggressiveness: 0.7,
    maintainTone: true,
    preserveFormatting: true
  });

  /**
   * Handle text humanization
   */
  const handleHumanize = useCallback(async () => {
    if (!inputText.trim()) {
      setError('Please enter some text to humanize');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setOutputText('');
    setProcessingStats(null);
    setQualityMetrics(null);

    try {
      const response = await fetch('/api/humanize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: inputText,
          options: {
            userTier,
            ...options
          }
        })
      });

      const data = await response.json();

      if (data.success) {
        setOutputText(data.humanizedText);
        setProcessingStats(data.transformationStats);
        setQualityMetrics(data.qualityMetrics);
      } else {
        setError(data.error || 'Humanization failed');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setIsProcessing(false);
    }
  }, [inputText, userTier, options]);

  /**
   * Handle text analysis
   */
  const handleAnalyze = useCallback(async () => {
    if (!inputText.trim()) {
      setError('Please enter some text to analyze');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const response = await fetch('/api/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: inputText,
          options: {
            type: 'comprehensive',
            targetDetection: options.targetDetection
          }
        })
      });

      const data = await response.json();

      if (data.success) {
        // Display analysis results
        alert(`Analysis Results:
AI Detection Score: ${data.aiDetection?.score || 'N/A'}%
Quality Level: ${data.qualityLevel || 'N/A'}
Risk Level: ${data.aiDetection?.riskLevel || 'N/A'}
Recommendations: ${data.recommendations?.length || 0}`);
      } else {
        setError(data.error || 'Analysis failed');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setIsProcessing(false);
    }
  }, [inputText, options.targetDetection]);

  /**
   * Copy text to clipboard
   */
  const copyToClipboard = useCallback(async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      alert('Text copied to clipboard!');
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Text copied to clipboard!');
    }
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>GhostLayer 2.0 - AI Text Humanization</h1>
        <p>Transform AI-generated text to achieve ≤10% detection scores while maintaining professional quality</p>
      </div>

      {/* User Tier Selection */}
      <div className={styles.tierSelection}>
        <label htmlFor="userTier">User Tier:</label>
        <select
          id="userTier"
          value={userTier}
          onChange={(e) => setUserTier(e.target.value)}
          className={styles.select}
        >
          <option value="freemium">Freemium (2 passes, ≤15% detection)</option>
          <option value="premium">Premium (3 passes, ≤10% detection)</option>
          <option value="admin">Admin (4 passes, ≤5% detection)</option>
        </select>
      </div>

      {/* Configuration Options */}
      <div className={styles.options}>
        <h3>Configuration Options</h3>
        <div className={styles.optionGrid}>
          <div className={styles.option}>
            <label htmlFor="targetDetection">Target AI Detection (%):</label>
            <input
              id="targetDetection"
              type="number"
              min="1"
              max="50"
              value={options.targetDetection}
              onChange={(e) => setOptions(prev => ({
                ...prev,
                targetDetection: parseInt(e.target.value)
              }))}
              className={styles.input}
            />
          </div>
          
          <div className={styles.option}>
            <label htmlFor="aggressiveness">Aggressiveness (0-1):</label>
            <input
              id="aggressiveness"
              type="number"
              min="0.1"
              max="1"
              step="0.1"
              value={options.aggressiveness}
              onChange={(e) => setOptions(prev => ({
                ...prev,
                aggressiveness: parseFloat(e.target.value)
              }))}
              className={styles.input}
            />
          </div>
          
          <div className={styles.option}>
            <label>
              <input
                type="checkbox"
                checked={options.maintainTone}
                onChange={(e) => setOptions(prev => ({
                  ...prev,
                  maintainTone: e.target.checked
                }))}
              />
              Maintain Professional Tone
            </label>
          </div>
          
          <div className={styles.option}>
            <label>
              <input
                type="checkbox"
                checked={options.preserveFormatting}
                onChange={(e) => setOptions(prev => ({
                  ...prev,
                  preserveFormatting: e.target.checked
                }))}
              />
              Preserve Formatting
            </label>
          </div>
        </div>
      </div>

      {/* Input Section */}
      <div className={styles.inputSection}>
        <h3>Input Text</h3>
        <textarea
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          placeholder="Paste your AI-generated text here..."
          className={styles.textarea}
          rows={8}
        />
        <div className={styles.textInfo}>
          Characters: {inputText.length} | Words: {inputText.split(/\s+/).filter(w => w).length}
        </div>
      </div>

      {/* Action Buttons */}
      <div className={styles.actions}>
        <button
          onClick={handleHumanize}
          disabled={isProcessing || !inputText.trim()}
          className={`${styles.button} ${styles.primary}`}
        >
          {isProcessing ? 'Processing...' : 'Humanize Text'}
        </button>
        
        <button
          onClick={handleAnalyze}
          disabled={isProcessing || !inputText.trim()}
          className={`${styles.button} ${styles.secondary}`}
        >
          Analyze Text
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className={styles.error}>
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Output Section */}
      {outputText && (
        <div className={styles.outputSection}>
          <div className={styles.outputHeader}>
            <h3>Humanized Text</h3>
            <button
              onClick={() => copyToClipboard(outputText)}
              className={`${styles.button} ${styles.small}`}
            >
              Copy
            </button>
          </div>
          <textarea
            value={outputText}
            readOnly
            className={styles.textarea}
            rows={8}
          />
        </div>
      )}

      {/* Processing Statistics */}
      {processingStats && (
        <div className={styles.stats}>
          <h3>Transformation Statistics</h3>
          <div className={styles.statsGrid}>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Transformation Rate:</span>
              <span className={styles.statValue}>{processingStats.transformationRate}%</span>
            </div>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Words Replaced:</span>
              <span className={styles.statValue}>{processingStats.wordsReplaced}</span>
            </div>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Sentences Transformed:</span>
              <span className={styles.statValue}>{processingStats.sentencesTransformed}</span>
            </div>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Processing Passes:</span>
              <span className={styles.statValue}>{processingStats.passes}</span>
            </div>
            {processingStats.hesitationMarkersAdded > 0 && (
              <div className={styles.stat}>
                <span className={styles.statLabel}>Hesitation Markers:</span>
                <span className={styles.statValue}>{processingStats.hesitationMarkersAdded}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quality Metrics */}
      {qualityMetrics && (
        <div className={styles.metrics}>
          <h3>Quality Metrics</h3>
          <div className={styles.metricsGrid}>
            <div className={`${styles.metric} ${qualityMetrics.aiDetectionScore <= options.targetDetection ? styles.success : styles.warning}`}>
              <span className={styles.metricLabel}>AI Detection Score:</span>
              <span className={styles.metricValue}>{qualityMetrics.aiDetectionScore}%</span>
            </div>
            <div className={styles.metric}>
              <span className={styles.metricLabel}>Grammar Score:</span>
              <span className={styles.metricValue}>{qualityMetrics.grammarScore}%</span>
            </div>
            <div className={styles.metric}>
              <span className={styles.metricLabel}>Readability Score:</span>
              <span className={styles.metricValue}>{qualityMetrics.readabilityScore}%</span>
            </div>
            <div className={styles.metric}>
              <span className={styles.metricLabel}>Tone Consistency:</span>
              <span className={styles.metricValue}>{qualityMetrics.toneConsistency}%</span>
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className={styles.footer}>
        <p>
          <strong>GhostLayer 2.0</strong> - Advanced AI text humanization achieving ≤10% detection scores
        </p>
        <p>
          Built with JavaScript-native algorithms for reliable localhost and Netlify deployment
        </p>
      </div>
    </div>
  );
};

export default HumanizationDemo;
