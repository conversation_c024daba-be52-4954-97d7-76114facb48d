/**
 * Advanced Text Analyzer
 * 
 * Comprehensive text analysis system for AI detection pattern recognition
 * and quality assessment. Identifies AI-typical patterns and provides
 * metrics for humanization effectiveness.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

/**
 * Text Analyzer class for comprehensive text analysis
 */
export class TextAnalyzer {
    constructor() {
        this.initializeAIPatterns();
        this.initializeQualityMetrics();
        this.initializeReadabilityRules();
    }

    /**
     * Initialize AI detection patterns
     */
    initializeAIPatterns() {
        // Common AI-generated text patterns
        this.aiPatterns = {
            // Formal transitions (high AI probability)
            formalTransitions: {
                patterns: [
                    /\b(furthermore|moreover|additionally|consequently|therefore)\b/gi,
                    /\b(however|nevertheless|nonetheless|meanwhile|subsequently)\b/gi,
                    /\b(in conclusion|to summarize|in summary|overall|ultimately)\b/gi
                ],
                weight: 0.8,
                description: 'Formal transition words'
            },

            // Repetitive sentence structures
            repetitiveStructures: {
                patterns: [
                    /^(The|This|These|Those)\s+\w+\s+(is|are|was|were)/gm,
                    /^(It|They)\s+(is|are|was|were)\s+(important|significant|crucial)/gm,
                    /\b(can be|may be|might be)\s+\w+ed\b/gi
                ],
                weight: 0.6,
                description: 'Repetitive sentence beginnings'
            },

            // Overuse of qualifiers
            excessiveQualifiers: {
                patterns: [
                    /\b(very|extremely|highly|particularly|especially|significantly)\b/gi,
                    /\b(quite|rather|somewhat|fairly|relatively|considerably)\b/gi,
                    /\b(clearly|obviously|certainly|definitely|absolutely)\b/gi
                ],
                weight: 0.5,
                description: 'Excessive use of qualifiers'
            },

            // Technical jargon clustering
            technicalClustering: {
                patterns: [
                    /\b(utilize|implement|demonstrate|analyze|optimize)\b/gi,
                    /\b(comprehensive|sophisticated|advanced|innovative|cutting-edge)\b/gi,
                    /\b(framework|paradigm|methodology|approach|strategy)\b/gi
                ],
                weight: 0.7,
                description: 'Technical jargon clustering'
            },

            // Passive voice overuse
            passiveVoice: {
                patterns: [
                    /\b(is|are|was|were|being|been)\s+\w+ed\b/gi,
                    /\b(can be|may be|might be|will be|would be)\s+\w+ed\b/gi
                ],
                weight: 0.4,
                description: 'Passive voice constructions'
            },

            // Lack of contractions (too formal)
            lackOfContractions: {
                patterns: [
                    /\b(do not|does not|did not|will not|would not|cannot|could not|should not)\b/gi,
                    /\b(I am|you are|he is|she is|it is|we are|they are)\b/gi,
                    /\b(I have|you have|he has|she has|it has|we have|they have)\b/gi
                ],
                weight: 0.3,
                description: 'Lack of natural contractions'
            },

            // Redundant phrases
            redundantPhrases: {
                patterns: [
                    /\b(in order to|for the purpose of|with the aim of)\b/gi,
                    /\b(it is important to note that|it should be noted that)\b/gi,
                    /\b(a wide variety of|a large number of|a significant amount of)\b/gi
                ],
                weight: 0.6,
                description: 'Redundant or wordy phrases'
            }
        };

        // AI trigger words with high detection probability
        this.highRiskWords = new Set([
            'delve', 'realm', 'landscape', 'paradigm', 'framework',
            'comprehensive', 'sophisticated', 'cutting-edge', 'state-of-the-art',
            'furthermore', 'moreover', 'additionally', 'consequently',
            'utilize', 'implement', 'demonstrate', 'optimize', 'leverage'
        ]);
    }

    /**
     * Initialize quality assessment metrics
     */
    initializeQualityMetrics() {
        this.qualityMetrics = {
            // Readability indicators
            readability: {
                averageSentenceLength: { min: 15, max: 25, weight: 0.3 },
                averageWordLength: { min: 4, max: 6, weight: 0.2 },
                syllableComplexity: { min: 1.3, max: 1.8, weight: 0.2 }
            },

            // Natural language indicators
            naturalness: {
                contractionRatio: { min: 0.1, max: 0.3, weight: 0.4 },
                personalPronounRatio: { min: 0.02, max: 0.08, weight: 0.3 },
                questionRatio: { min: 0.05, max: 0.15, weight: 0.2 }
            },

            // Vocabulary diversity
            diversity: {
                uniqueWordRatio: { min: 0.6, max: 0.9, weight: 0.4 },
                adjectiveVariety: { min: 0.7, max: 1.0, weight: 0.3 },
                verbVariety: { min: 0.7, max: 1.0, weight: 0.3 }
            }
        };
    }

    /**
     * Initialize readability assessment rules
     */
    initializeReadabilityRules() {
        this.readabilityRules = {
            // Flesch Reading Ease score ranges
            fleschRanges: {
                veryEasy: { min: 90, max: 100 },
                easy: { min: 80, max: 90 },
                fairlyEasy: { min: 70, max: 80 },
                standard: { min: 60, max: 70 },
                fairlyDifficult: { min: 50, max: 60 },
                difficult: { min: 30, max: 50 },
                veryDifficult: { min: 0, max: 30 }
            },

            // Ideal ranges for different content types
            targetRanges: {
                general: { min: 60, max: 80 },
                academic: { min: 40, max: 60 },
                technical: { min: 30, max: 50 },
                casual: { min: 70, max: 90 }
            }
        };
    }

    /**
     * Perform comprehensive text analysis
     * @param {string} text - Input text to analyze
     * @returns {Promise<Object>} Analysis results
     */
    async analyzeText(text) {
        const startTime = Date.now();

        try {
            // Basic text statistics
            const basicStats = this.getBasicStatistics(text);
            
            // AI pattern detection
            const aiAnalysis = this.detectAIPatterns(text);
            
            // Quality assessment
            const qualityAssessment = this.assessQuality(text, basicStats);
            
            // Readability analysis
            const readabilityAnalysis = this.analyzeReadability(text, basicStats);
            
            // Risk assessment
            const riskAssessment = this.assessDetectionRisk(aiAnalysis, qualityAssessment);

            return {
                success: true,
                processingTime: Date.now() - startTime,
                basicStats,
                aiPatterns: aiAnalysis,
                quality: qualityAssessment,
                readability: readabilityAnalysis,
                risk: riskAssessment,
                recommendations: this.generateRecommendations(aiAnalysis, qualityAssessment)
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                processingTime: Date.now() - startTime
            };
        }
    }

    /**
     * Get basic text statistics
     * @param {string} text - Input text
     * @returns {Object} Basic statistics
     */
    getBasicStatistics(text) {
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const words = text.split(/\s+/).filter(w => w.trim().length > 0);
        const characters = text.length;
        const charactersNoSpaces = text.replace(/\s/g, '').length;

        // Calculate averages
        const avgSentenceLength = words.length / sentences.length;
        const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;

        // Count syllables (approximation)
        const syllableCount = words.reduce((sum, word) => sum + this.countSyllables(word), 0);
        const avgSyllablesPerWord = syllableCount / words.length;

        return {
            sentenceCount: sentences.length,
            wordCount: words.length,
            characterCount: characters,
            characterCountNoSpaces: charactersNoSpaces,
            avgSentenceLength: Math.round(avgSentenceLength * 100) / 100,
            avgWordLength: Math.round(avgWordLength * 100) / 100,
            avgSyllablesPerWord: Math.round(avgSyllablesPerWord * 100) / 100,
            syllableCount
        };
    }

    /**
     * Detect AI patterns in text
     * @param {string} text - Input text
     * @returns {Object} AI pattern analysis
     */
    detectAIPatterns(text) {
        const detectedPatterns = {};
        let totalScore = 0;
        let totalMatches = 0;

        // Analyze each pattern category
        for (const [category, patternData] of Object.entries(this.aiPatterns)) {
            const matches = [];
            let categoryScore = 0;

            for (const pattern of patternData.patterns) {
                const patternMatches = [...text.matchAll(pattern)];
                matches.push(...patternMatches.map(match => ({
                    text: match[0],
                    index: match.index,
                    pattern: pattern.source
                })));
            }

            if (matches.length > 0) {
                categoryScore = Math.min(matches.length * patternData.weight, 10);
                totalScore += categoryScore;
                totalMatches += matches.length;

                detectedPatterns[category] = {
                    matches: matches.length,
                    score: Math.round(categoryScore * 100) / 100,
                    weight: patternData.weight,
                    description: patternData.description,
                    examples: matches.slice(0, 5).map(m => m.text)
                };
            }
        }

        // Check for high-risk words
        const highRiskMatches = [];
        for (const word of this.highRiskWords) {
            const regex = new RegExp(`\\b${word}\\b`, 'gi');
            const matches = [...text.matchAll(regex)];
            highRiskMatches.push(...matches.map(m => m[0]));
        }

        return {
            totalScore: Math.round(totalScore * 100) / 100,
            totalMatches,
            patternCount: Object.keys(detectedPatterns).length,
            patterns: detectedPatterns,
            highRiskWords: [...new Set(highRiskMatches)],
            estimatedAIDetection: Math.min(Math.round((totalScore / 50) * 100), 100)
        };
    }

    /**
     * Assess text quality
     * @param {string} text - Input text
     * @param {Object} basicStats - Basic statistics
     * @returns {Object} Quality assessment
     */
    assessQuality(text, basicStats) {
        const quality = {
            readabilityScore: 0,
            naturalnessScore: 0,
            diversityScore: 0,
            overallScore: 0
        };

        // Readability assessment
        const readabilityFactors = {
            sentenceLength: this.scoreMetric(basicStats.avgSentenceLength, 15, 25),
            wordLength: this.scoreMetric(basicStats.avgWordLength, 4, 6),
            syllableComplexity: this.scoreMetric(basicStats.avgSyllablesPerWord, 1.3, 1.8)
        };
        quality.readabilityScore = this.calculateWeightedScore(readabilityFactors, this.qualityMetrics.readability);

        // Naturalness assessment
        const contractionCount = (text.match(/\b\w+'(t|re|ve|ll|d|s)\b/g) || []).length;
        const contractionRatio = contractionCount / basicStats.wordCount;
        
        const personalPronouns = (text.match(/\b(I|you|we|they|me|us|them)\b/gi) || []).length;
        const personalPronounRatio = personalPronouns / basicStats.wordCount;
        
        const questions = (text.match(/\?/g) || []).length;
        const questionRatio = questions / basicStats.sentenceCount;

        const naturalnessFactors = {
            contractionRatio: this.scoreMetric(contractionRatio, 0.1, 0.3),
            personalPronounRatio: this.scoreMetric(personalPronounRatio, 0.02, 0.08),
            questionRatio: this.scoreMetric(questionRatio, 0.05, 0.15)
        };
        quality.naturalnessScore = this.calculateWeightedScore(naturalnessFactors, this.qualityMetrics.naturalness);

        // Diversity assessment
        const words = text.toLowerCase().split(/\s+/).filter(w => w.trim().length > 0);
        const uniqueWords = new Set(words);
        const uniqueWordRatio = uniqueWords.size / words.length;

        const diversityFactors = {
            uniqueWordRatio: this.scoreMetric(uniqueWordRatio, 0.6, 0.9),
            adjectiveVariety: 0.8, // Placeholder - would need POS tagging
            verbVariety: 0.8 // Placeholder - would need POS tagging
        };
        quality.diversityScore = this.calculateWeightedScore(diversityFactors, this.qualityMetrics.diversity);

        // Overall score
        quality.overallScore = Math.round(
            (quality.readabilityScore * 0.4 + quality.naturalnessScore * 0.4 + quality.diversityScore * 0.2) * 100
        ) / 100;

        return quality;
    }

    /**
     * Analyze text readability
     * @param {string} text - Input text
     * @param {Object} basicStats - Basic statistics
     * @returns {Object} Readability analysis
     */
    analyzeReadability(text, basicStats) {
        // Flesch Reading Ease Score
        const fleschScore = 206.835 - 
            (1.015 * basicStats.avgSentenceLength) - 
            (84.6 * basicStats.avgSyllablesPerWord);

        // Determine readability level
        let level = 'standard';
        for (const [levelName, range] of Object.entries(this.readabilityRules.fleschRanges)) {
            if (fleschScore >= range.min && fleschScore <= range.max) {
                level = levelName;
                break;
            }
        }

        return {
            fleschScore: Math.round(fleschScore * 100) / 100,
            level,
            grade: this.getGradeLevel(fleschScore),
            recommendation: this.getReadabilityRecommendation(fleschScore)
        };
    }

    /**
     * Assess detection risk
     * @param {Object} aiAnalysis - AI pattern analysis
     * @param {Object} qualityAssessment - Quality assessment
     * @returns {Object} Risk assessment
     */
    assessDetectionRisk(aiAnalysis, qualityAssessment) {
        const riskFactors = {
            aiPatternRisk: Math.min(aiAnalysis.totalScore / 20, 1),
            qualityRisk: Math.max(0, (80 - qualityAssessment.overallScore) / 80),
            highRiskWordRisk: Math.min(aiAnalysis.highRiskWords.length / 10, 1)
        };

        const overallRisk = (riskFactors.aiPatternRisk * 0.5 + 
                           riskFactors.qualityRisk * 0.3 + 
                           riskFactors.highRiskWordRisk * 0.2);

        return {
            overallRisk: Math.round(overallRisk * 100),
            riskLevel: this.getRiskLevel(overallRisk),
            factors: riskFactors,
            estimatedDetectionProbability: Math.round(overallRisk * 100)
        };
    }

    /**
     * Generate improvement recommendations
     * @param {Object} aiAnalysis - AI analysis
     * @param {Object} qualityAssessment - Quality assessment
     * @returns {Array} Recommendations
     */
    generateRecommendations(aiAnalysis, qualityAssessment) {
        const recommendations = [];

        // AI pattern recommendations
        if (aiAnalysis.totalScore > 10) {
            recommendations.push({
                type: 'ai-patterns',
                priority: 'high',
                message: 'Reduce formal transition words and repetitive structures'
            });
        }

        // Quality recommendations
        if (qualityAssessment.naturalnessScore < 0.6) {
            recommendations.push({
                type: 'naturalness',
                priority: 'medium',
                message: 'Add more contractions and personal pronouns for natural tone'
            });
        }

        // High-risk word recommendations
        if (aiAnalysis.highRiskWords.length > 5) {
            recommendations.push({
                type: 'vocabulary',
                priority: 'high',
                message: 'Replace high-risk AI trigger words with alternatives'
            });
        }

        return recommendations;
    }

    /**
     * Count syllables in a word (approximation)
     * @param {string} word - Word to analyze
     * @returns {number} Syllable count
     */
    countSyllables(word) {
        word = word.toLowerCase();
        if (word.length <= 3) return 1;
        
        const vowels = 'aeiouy';
        let count = 0;
        let previousWasVowel = false;
        
        for (let i = 0; i < word.length; i++) {
            const isVowel = vowels.includes(word[i]);
            if (isVowel && !previousWasVowel) {
                count++;
            }
            previousWasVowel = isVowel;
        }
        
        // Adjust for silent 'e'
        if (word.endsWith('e')) count--;
        
        return Math.max(1, count);
    }

    /**
     * Score a metric against ideal range
     * @param {number} value - Actual value
     * @param {number} min - Minimum ideal value
     * @param {number} max - Maximum ideal value
     * @returns {number} Score (0-1)
     */
    scoreMetric(value, min, max) {
        if (value >= min && value <= max) return 1;
        if (value < min) return Math.max(0, value / min);
        return Math.max(0, max / value);
    }

    /**
     * Calculate weighted score
     * @param {Object} factors - Factor scores
     * @param {Object} weights - Weight configuration
     * @returns {number} Weighted score
     */
    calculateWeightedScore(factors, weights) {
        let totalScore = 0;
        let totalWeight = 0;

        for (const [factor, score] of Object.entries(factors)) {
            const weight = weights[factor]?.weight || 1;
            totalScore += score * weight;
            totalWeight += weight;
        }

        return Math.round((totalScore / totalWeight) * 100) / 100;
    }

    /**
     * Get grade level from Flesch score
     * @param {number} fleschScore - Flesch reading ease score
     * @returns {string} Grade level
     */
    getGradeLevel(fleschScore) {
        if (fleschScore >= 90) return '5th grade';
        if (fleschScore >= 80) return '6th grade';
        if (fleschScore >= 70) return '7th grade';
        if (fleschScore >= 60) return '8th-9th grade';
        if (fleschScore >= 50) return '10th-12th grade';
        if (fleschScore >= 30) return 'College level';
        return 'Graduate level';
    }

    /**
     * Get readability recommendation
     * @param {number} fleschScore - Flesch reading ease score
     * @returns {string} Recommendation
     */
    getReadabilityRecommendation(fleschScore) {
        if (fleschScore < 50) return 'Consider simplifying sentences and using shorter words';
        if (fleschScore > 80) return 'Text may be too simple for the target audience';
        return 'Readability is appropriate for general audience';
    }

    /**
     * Get risk level description
     * @param {number} risk - Risk score (0-1)
     * @returns {string} Risk level
     */
    getRiskLevel(risk) {
        if (risk < 0.3) return 'Low';
        if (risk < 0.6) return 'Medium';
        if (risk < 0.8) return 'High';
        return 'Very High';
    }
}

// Export singleton instance
export const textAnalyzer = new TextAnalyzer();
