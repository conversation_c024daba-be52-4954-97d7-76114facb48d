/**
 * Sentence Structure Transformer
 * 
 * Advanced sentence restructuring system for AI detection avoidance.
 * Implements various transformation techniques while preserving meaning
 * and maintaining professional tone.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

/**
 * Sentence Transformer class for structural text modifications
 */
export class SentenceTransformer {
    constructor() {
        this.initializeTransformationRules();
        this.initializeSentencePatterns();
    }

    /**
     * Initialize transformation rules and patterns
     */
    initializeTransformationRules() {
        // Transformation techniques with their weights
        this.transformations = {
            passiveToActive: { weight: 0.3, enabled: true },
            activeToPassive: { weight: 0.2, enabled: true },
            clauseReordering: { weight: 0.4, enabled: true },
            sentenceSplitting: { weight: 0.3, enabled: true },
            sentenceCombining: { weight: 0.2, enabled: true },
            conditionalRestructuring: { weight: 0.3, enabled: true },
            questionToStatement: { weight: 0.2, enabled: true },
            negationRestructuring: { weight: 0.3, enabled: true }
        };

        // Common sentence starters that indicate AI-generated content
        this.aiSentenceStarters = new Set([
            'furthermore', 'moreover', 'additionally', 'consequently', 'therefore',
            'however', 'nevertheless', 'nonetheless', 'meanwhile', 'subsequently',
            'in conclusion', 'to summarize', 'in summary', 'overall', 'ultimately'
        ]);

        // Alternative sentence starters for variety
        this.alternativeStarters = {
            'furthermore': ['What\'s more', 'Beyond that', 'On top of this', 'Also worth noting'],
            'moreover': ['What\'s more', 'Beyond that', 'Additionally', 'Also'],
            'additionally': ['Also', 'Plus', 'What\'s more', 'Beyond that'],
            'however': ['But', 'Yet', 'Still', 'Though', 'On the flip side'],
            'therefore': ['So', 'Thus', 'As a result', 'Because of this'],
            'consequently': ['As a result', 'So', 'Thus', 'Because of this'],
            'nevertheless': ['Still', 'Yet', 'Even so', 'Despite this'],
            'nonetheless': ['Still', 'Yet', 'Even so', 'Despite this']
        };
    }

    /**
     * Initialize sentence pattern recognition
     */
    initializeSentencePatterns() {
        this.patterns = {
            // Passive voice patterns
            passive: /\b(is|are|was|were|being|been)\s+\w+ed\b/gi,
            
            // Complex sentence patterns
            complexConjunctions: /\b(although|because|since|while|whereas|unless|until)\b/gi,
            
            // Conditional patterns
            conditionals: /\b(if|when|unless|provided|assuming)\b/gi,
            
            // Question patterns
            questions: /^(what|how|why|where|when|who|which|can|could|would|should|do|does|did)\b/gi,
            
            // Negation patterns
            negations: /\b(not|never|no|none|nothing|nobody|nowhere|neither)\b/gi,
            
            // Formal transitions
            formalTransitions: /\b(furthermore|moreover|additionally|consequently|therefore|however|nevertheless|nonetheless)\b/gi
        };
    }

    /**
     * Transform sentences in text
     * @param {string} text - Input text
     * @param {Object} options - Transformation options
     * @returns {Promise<Object>} Transformation result
     */
    async transformSentences(text, options = {}) {
        const config = {
            aggressiveness: 0.7,
            maintainTone: true,
            passNumber: 1,
            ...options
        };

        const sentences = this.splitIntoSentences(text);
        let transformedSentences = [];
        let transformationCount = 0;

        for (let i = 0; i < sentences.length; i++) {
            const sentence = sentences[i].trim();
            if (!sentence) continue;

            const transformed = await this.transformSingleSentence(
                sentence, 
                config, 
                i, 
                sentences.length
            );

            if (transformed.wasTransformed) {
                transformationCount++;
            }

            transformedSentences.push(transformed.sentence);
        }

        return {
            text: transformedSentences.join(' '),
            transformations: transformationCount,
            originalSentences: sentences.length,
            transformationRate: Math.round((transformationCount / sentences.length) * 100)
        };
    }

    /**
     * Transform a single sentence
     * @param {string} sentence - Input sentence
     * @param {Object} config - Configuration
     * @param {number} index - Sentence index
     * @param {number} total - Total sentences
     * @returns {Promise<Object>} Transformation result
     */
    async transformSingleSentence(sentence, config, index, total) {
        let transformedSentence = sentence;
        let wasTransformed = false;

        // Skip very short sentences
        if (sentence.length < 10) {
            return { sentence, wasTransformed: false };
        }

        // Apply transformations based on aggressiveness
        const shouldTransform = Math.random() < config.aggressiveness;
        if (!shouldTransform) {
            return { sentence, wasTransformed: false };
        }

        // Try different transformation techniques
        const techniques = this.selectTransformationTechniques(sentence, config);
        
        for (const technique of techniques) {
            const result = await this.applyTransformation(transformedSentence, technique, config);
            if (result.success) {
                transformedSentence = result.sentence;
                wasTransformed = true;
                break; // Apply only one transformation per sentence
            }
        }

        return { sentence: transformedSentence, wasTransformed };
    }

    /**
     * Select appropriate transformation techniques for a sentence
     * @param {string} sentence - Input sentence
     * @param {Object} config - Configuration
     * @returns {Array} Selected techniques
     */
    selectTransformationTechniques(sentence, config) {
        const techniques = [];
        const lowerSentence = sentence.toLowerCase();

        // Check for AI sentence starters
        const firstWord = lowerSentence.split(' ')[0];
        if (this.aiSentenceStarters.has(firstWord)) {
            techniques.push('replaceAIStarter');
        }

        // Check for passive voice
        if (this.patterns.passive.test(sentence)) {
            techniques.push('passiveToActive');
        }

        // Check for complex sentences that can be split
        if (sentence.length > 100 && this.patterns.complexConjunctions.test(sentence)) {
            techniques.push('sentenceSplitting');
        }

        // Check for questions
        if (this.patterns.questions.test(sentence)) {
            techniques.push('questionToStatement');
        }

        // Check for negations
        if (this.patterns.negations.test(sentence)) {
            techniques.push('negationRestructuring');
        }

        // Default transformations
        if (techniques.length === 0) {
            techniques.push('clauseReordering', 'activeToPassive');
        }

        // Shuffle and limit techniques
        return this.shuffleArray(techniques).slice(0, 2);
    }

    /**
     * Apply a specific transformation technique
     * @param {string} sentence - Input sentence
     * @param {string} technique - Transformation technique
     * @param {Object} config - Configuration
     * @returns {Promise<Object>} Transformation result
     */
    async applyTransformation(sentence, technique, config) {
        try {
            switch (technique) {
                case 'replaceAIStarter':
                    return this.replaceAIStarter(sentence);
                
                case 'passiveToActive':
                    return this.convertPassiveToActive(sentence);
                
                case 'activeToPassive':
                    return this.convertActiveToPassive(sentence);
                
                case 'clauseReordering':
                    return this.reorderClauses(sentence);
                
                case 'sentenceSplitting':
                    return this.splitComplexSentence(sentence);
                
                case 'questionToStatement':
                    return this.convertQuestionToStatement(sentence);
                
                case 'negationRestructuring':
                    return this.restructureNegation(sentence);
                
                default:
                    return { success: false, sentence };
            }
        } catch (error) {
            return { success: false, sentence, error: error.message };
        }
    }

    /**
     * Replace AI-typical sentence starters
     * @param {string} sentence - Input sentence
     * @returns {Object} Transformation result
     */
    replaceAIStarter(sentence) {
        const words = sentence.split(' ');
        const firstWord = words[0].toLowerCase();
        
        if (this.alternativeStarters[firstWord]) {
            const alternatives = this.alternativeStarters[firstWord];
            const replacement = alternatives[Math.floor(Math.random() * alternatives.length)];
            
            words[0] = replacement;
            return {
                success: true,
                sentence: words.join(' ')
            };
        }
        
        return { success: false, sentence };
    }

    /**
     * Convert passive voice to active voice
     * @param {string} sentence - Input sentence
     * @returns {Object} Transformation result
     */
    convertPassiveToActive(sentence) {
        // Simple passive to active conversion
        // This is a basic implementation - could be enhanced with more sophisticated NLP
        
        const passivePatterns = [
            {
                pattern: /(.+)\s+(is|are|was|were)\s+(\w+ed)\s+by\s+(.+)/i,
                replacement: '$4 $2 $3 $1'
            },
            {
                pattern: /(.+)\s+(is|are|was|were)\s+(\w+ed)/i,
                replacement: 'Someone $3 $1'
            }
        ];

        for (const { pattern, replacement } of passivePatterns) {
            if (pattern.test(sentence)) {
                const transformed = sentence.replace(pattern, replacement);
                if (transformed !== sentence) {
                    return { success: true, sentence: transformed };
                }
            }
        }

        return { success: false, sentence };
    }

    /**
     * Convert active voice to passive voice
     * @param {string} sentence - Input sentence
     * @returns {Object} Transformation result
     */
    convertActiveToPassive(sentence) {
        // Basic active to passive conversion
        const activePatterns = [
            {
                pattern: /(.+)\s+(creates?|makes?|builds?|develops?)\s+(.+)/i,
                replacement: '$3 is $2d by $1'
            }
        ];

        for (const { pattern, replacement } of activePatterns) {
            if (pattern.test(sentence)) {
                const transformed = sentence.replace(pattern, replacement);
                if (transformed !== sentence) {
                    return { success: true, sentence: transformed };
                }
            }
        }

        return { success: false, sentence };
    }

    /**
     * Reorder clauses within a sentence
     * @param {string} sentence - Input sentence
     * @returns {Object} Transformation result
     */
    reorderClauses(sentence) {
        // Look for sentences with conjunctions that can be reordered
        const conjunctionPatterns = [
            {
                pattern: /(.+),\s+(but|however|yet)\s+(.+)/i,
                replacement: '$2, $1, $3'
            },
            {
                pattern: /(.+)\s+(because|since|as)\s+(.+)/i,
                replacement: 'Since $3, $1'
            }
        ];

        for (const { pattern, replacement } of conjunctionPatterns) {
            if (pattern.test(sentence)) {
                const transformed = sentence.replace(pattern, replacement);
                if (transformed !== sentence) {
                    return { success: true, sentence: transformed };
                }
            }
        }

        return { success: false, sentence };
    }

    /**
     * Split complex sentences
     * @param {string} sentence - Input sentence
     * @returns {Object} Transformation result
     */
    splitComplexSentence(sentence) {
        // Split on conjunctions
        const splitPatterns = [
            /(.+),\s+and\s+(.+)/i,
            /(.+),\s+but\s+(.+)/i,
            /(.+)\s+because\s+(.+)/i
        ];

        for (const pattern of splitPatterns) {
            const match = sentence.match(pattern);
            if (match) {
                const [, part1, part2] = match;
                return {
                    success: true,
                    sentence: `${part1.trim()}. ${part2.trim()}`
                };
            }
        }

        return { success: false, sentence };
    }

    /**
     * Convert questions to statements
     * @param {string} sentence - Input sentence
     * @returns {Object} Transformation result
     */
    convertQuestionToStatement(sentence) {
        const questionPatterns = [
            {
                pattern: /What is (.+)\?/i,
                replacement: '$1 is defined as...'
            },
            {
                pattern: /How does (.+)\?/i,
                replacement: '$1 works by...'
            },
            {
                pattern: /Why (.+)\?/i,
                replacement: 'The reason $1 is...'
            }
        ];

        for (const { pattern, replacement } of questionPatterns) {
            if (pattern.test(sentence)) {
                const transformed = sentence.replace(pattern, replacement);
                if (transformed !== sentence) {
                    return { success: true, sentence: transformed };
                }
            }
        }

        return { success: false, sentence };
    }

    /**
     * Restructure negations
     * @param {string} sentence - Input sentence
     * @returns {Object} Transformation result
     */
    restructureNegation(sentence) {
        const negationPatterns = [
            {
                pattern: /(.+)\s+is not\s+(.+)/i,
                replacement: '$1 lacks $2'
            },
            {
                pattern: /(.+)\s+does not\s+(.+)/i,
                replacement: '$1 fails to $2'
            }
        ];

        for (const { pattern, replacement } of negationPatterns) {
            if (pattern.test(sentence)) {
                const transformed = sentence.replace(pattern, replacement);
                if (transformed !== sentence) {
                    return { success: true, sentence: transformed };
                }
            }
        }

        return { success: false, sentence };
    }

    /**
     * Split text into sentences
     * @param {string} text - Input text
     * @returns {Array} Array of sentences
     */
    splitIntoSentences(text) {
        // Basic sentence splitting - could be enhanced
        return text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    }

    /**
     * Shuffle array elements
     * @param {Array} array - Array to shuffle
     * @returns {Array} Shuffled array
     */
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    /**
     * Get transformation statistics
     * @returns {Object} Statistics
     */
    getStatistics() {
        return {
            availableTransformations: Object.keys(this.transformations).length,
            enabledTransformations: Object.values(this.transformations).filter(t => t.enabled).length,
            aiStartersCount: this.aiSentenceStarters.size,
            alternativeStartersCount: Object.keys(this.alternativeStarters).length
        };
    }
}

// Export singleton instance
export const sentenceTransformer = new SentenceTransformer();
