/* GhostLayer 2.0 Humanization Demo Styles */

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e0e0e0;
}

.header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.header p {
  color: #666;
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

/* Tier Selection */
.tierSelection {
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.tierSelection label {
  font-weight: 600;
  margin-right: 1rem;
  color: #2c3e50;
}

.select {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background: white;
  min-width: 300px;
}

.select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Configuration Options */
.options {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.options h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.optionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.option {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.option label {
  font-weight: 500;
  color: #555;
}

.input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Input Section */
.inputSection {
  margin-bottom: 2rem;
}

.inputSection h3 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.textarea {
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  resize: vertical;
  min-height: 120px;
}

.textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.textInfo {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #666;
  text-align: right;
}

/* Action Buttons */
.actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.button {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 150px;
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.primary {
  background: #3498db;
  color: white;
}

.primary:hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-1px);
}

.secondary {
  background: #95a5a6;
  color: white;
}

.secondary:hover:not(:disabled) {
  background: #7f8c8d;
  transform: translateY(-1px);
}

.small {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  min-width: auto;
}

/* Error Display */
.error {
  background: #e74c3c;
  color: white;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 2rem;
  font-weight: 500;
}

/* Output Section */
.outputSection {
  margin-bottom: 2rem;
}

.outputHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.outputHeader h3 {
  margin: 0;
  color: #2c3e50;
}

/* Statistics */
.stats {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #e8f5e8;
  border-radius: 8px;
  border-left: 4px solid #27ae60;
}

.stats h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #27ae60;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
}

.statLabel {
  font-weight: 500;
  color: #555;
}

.statValue {
  font-weight: 700;
  color: #27ae60;
  font-size: 1.1rem;
}

/* Quality Metrics */
.metrics {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #fff3cd;
  border-radius: 8px;
  border-left: 4px solid #f39c12;
}

.metrics h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #f39c12;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: 4px;
  border: 2px solid transparent;
}

.metric.success {
  border-color: #27ae60;
  background: #e8f5e8;
}

.metric.warning {
  border-color: #e74c3c;
  background: #fdf2f2;
}

.metricLabel {
  font-weight: 500;
  color: #555;
}

.metricValue {
  font-weight: 700;
  font-size: 1.1rem;
}

.success .metricValue {
  color: #27ae60;
}

.warning .metricValue {
  color: #e74c3c;
}

/* Footer */
.footer {
  text-align: center;
  padding: 2rem 0;
  border-top: 2px solid #e0e0e0;
  margin-top: 3rem;
  color: #666;
}

.footer p {
  margin: 0.5rem 0;
}

.footer strong {
  color: #2c3e50;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .actions {
    flex-direction: column;
    align-items: center;
  }
  
  .button {
    width: 100%;
    max-width: 300px;
  }
  
  .optionGrid {
    grid-template-columns: 1fr;
  }
  
  .statsGrid,
  .metricsGrid {
    grid-template-columns: 1fr;
  }
  
  .outputHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .stat,
  .metric {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .tierSelection {
    text-align: center;
  }
  
  .tierSelection label {
    display: block;
    margin-bottom: 0.5rem;
  }
  
  .select {
    width: 100%;
    min-width: auto;
  }
}
