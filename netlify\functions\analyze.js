/**
 * Netlify Function: Text Analysis
 * 
 * Serverless function for comprehensive text analysis including AI detection,
 * quality assessment, and readability scoring.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import { TextAnalyzer } from '../../src/utils/textAnalyzer.js';
import { QualityValidator } from '../../src/utils/qualityValidator.js';

// Initialize services
const textAnalyzer = new TextAnalyzer();
const qualityValidator = new QualityValidator();

/**
 * Main Netlify function handler for text analysis
 * @param {Object} event - Netlify event object
 * @param {Object} context - Netlify context object
 * @returns {Promise<Object>} Response object
 */
export const handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Only allow POST requests
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Method not allowed. Use POST.'
            })
        };
    }

    try {
        // Parse request body
        let requestData;
        try {
            requestData = JSON.parse(event.body);
        } catch (parseError) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Invalid JSON in request body'
                })
            };
        }

        // Validate required fields
        const { text, options = {} } = requestData;
        
        if (!text || typeof text !== 'string') {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Text field is required and must be a string'
                })
            };
        }

        // Validate text length
        if (text.length > 100000) { // 100KB limit for analysis
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Text too long. Maximum 100,000 characters allowed for analysis.'
                })
            };
        }

        if (text.length < 10) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Text too short. Minimum 10 characters required for meaningful analysis.'
                })
            };
        }

        // Determine analysis type
        const analysisType = options.type || 'comprehensive';
        const targetDetection = options.targetDetection || 10;

        const startTime = Date.now();
        let analysisResult;

        switch (analysisType) {
            case 'ai-detection':
                analysisResult = await performAIDetectionAnalysis(text, options);
                break;
            
            case 'quality':
                analysisResult = await performQualityAnalysis(text, targetDetection);
                break;
            
            case 'readability':
                analysisResult = await performReadabilityAnalysis(text);
                break;
            
            case 'comprehensive':
            default:
                analysisResult = await performComprehensiveAnalysis(text, targetDetection);
                break;
        }

        const totalProcessingTime = Date.now() - startTime;

        // Prepare response
        const response = {
            success: true,
            analysisType,
            ...analysisResult,
            metadata: {
                functionVersion: '2.0.0',
                processingTime: totalProcessingTime,
                timestamp: new Date().toISOString(),
                serverless: true,
                environment: 'netlify',
                textLength: text.length
            }
        };

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(response)
        };

    } catch (error) {
        console.error('Analysis function error:', error);
        
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Internal server error during analysis. Please try again later.',
                errorId: generateErrorId()
            })
        };
    }
};

/**
 * Perform AI detection analysis
 * @param {string} text - Text to analyze
 * @param {Object} options - Analysis options
 * @returns {Promise<Object>} AI detection results
 */
async function performAIDetectionAnalysis(text, options) {
    const analysis = await textAnalyzer.analyzeText(text);
    
    if (!analysis.success) {
        throw new Error('AI detection analysis failed');
    }

    return {
        aiDetectionScore: analysis.aiPatterns.estimatedAIDetection,
        confidence: calculateConfidence(analysis.aiPatterns.totalScore),
        patterns: analysis.aiPatterns.patterns,
        highRiskWords: analysis.aiPatterns.highRiskWords,
        recommendations: analysis.recommendations.filter(r => r.type === 'ai-patterns'),
        riskLevel: analysis.risk.riskLevel,
        detailedAnalysis: {
            totalMatches: analysis.aiPatterns.totalMatches,
            patternCount: analysis.aiPatterns.patternCount,
            totalScore: analysis.aiPatterns.totalScore
        }
    };
}

/**
 * Perform quality analysis
 * @param {string} text - Text to analyze
 * @param {number} targetDetection - Target detection threshold
 * @returns {Promise<Object>} Quality analysis results
 */
async function performQualityAnalysis(text, targetDetection) {
    const qualityResult = await qualityValidator.validateQuality(text, targetDetection);
    
    if (!qualityResult.success) {
        throw new Error('Quality analysis failed');
    }

    return {
        overallScore: qualityResult.overallScore,
        qualityLevel: qualityResult.qualityLevel,
        meetsTarget: qualityResult.meetsTarget,
        scores: {
            aiDetection: qualityResult.aiDetectionScore,
            grammar: qualityResult.grammarScore,
            readability: qualityResult.readabilityScore,
            toneConsistency: qualityResult.toneConsistency
        },
        recommendations: qualityResult.recommendations,
        details: qualityResult.details
    };
}

/**
 * Perform readability analysis
 * @param {string} text - Text to analyze
 * @returns {Promise<Object>} Readability analysis results
 */
async function performReadabilityAnalysis(text) {
    const analysis = await textAnalyzer.analyzeText(text);
    
    if (!analysis.success) {
        throw new Error('Readability analysis failed');
    }

    return {
        readabilityScore: analysis.readability.fleschScore,
        readabilityLevel: analysis.readability.level,
        gradeLevel: analysis.readability.grade,
        recommendation: analysis.readability.recommendation,
        statistics: {
            sentences: analysis.basicStats.sentenceCount,
            words: analysis.basicStats.wordCount,
            characters: analysis.basicStats.characterCount,
            avgSentenceLength: analysis.basicStats.avgSentenceLength,
            avgWordLength: analysis.basicStats.avgWordLength,
            avgSyllablesPerWord: analysis.basicStats.avgSyllablesPerWord
        }
    };
}

/**
 * Perform comprehensive analysis
 * @param {string} text - Text to analyze
 * @param {number} targetDetection - Target detection threshold
 * @returns {Promise<Object>} Comprehensive analysis results
 */
async function performComprehensiveAnalysis(text, targetDetection) {
    // Run both text analysis and quality validation
    const [textAnalysis, qualityAnalysis] = await Promise.all([
        textAnalyzer.analyzeText(text),
        qualityValidator.validateQuality(text, targetDetection)
    ]);

    if (!textAnalysis.success || !qualityAnalysis.success) {
        throw new Error('Comprehensive analysis failed');
    }

    return {
        // Overall assessment
        overallScore: qualityAnalysis.overallScore,
        qualityLevel: qualityAnalysis.qualityLevel,
        meetsTarget: qualityAnalysis.meetsTarget,
        
        // AI detection details
        aiDetection: {
            score: textAnalysis.aiPatterns.estimatedAIDetection,
            confidence: calculateConfidence(textAnalysis.aiPatterns.totalScore),
            riskLevel: textAnalysis.risk.riskLevel,
            patterns: textAnalysis.aiPatterns.patterns,
            highRiskWords: textAnalysis.aiPatterns.highRiskWords
        },
        
        // Quality metrics
        quality: {
            grammar: qualityAnalysis.grammarScore,
            readability: qualityAnalysis.readabilityScore,
            toneConsistency: qualityAnalysis.toneConsistency,
            naturalness: textAnalysis.quality.naturalnessScore,
            diversity: textAnalysis.quality.diversityScore
        },
        
        // Text statistics
        statistics: textAnalysis.basicStats,
        
        // Readability details
        readability: textAnalysis.readability,
        
        // Recommendations
        recommendations: [
            ...textAnalysis.recommendations,
            ...qualityAnalysis.recommendations
        ],
        
        // Risk assessment
        risk: textAnalysis.risk
    };
}

/**
 * Calculate confidence level for AI detection
 * @param {number} totalScore - Total AI pattern score
 * @returns {string} Confidence level
 */
function calculateConfidence(totalScore) {
    if (totalScore >= 20) return 'High';
    if (totalScore >= 10) return 'Medium';
    if (totalScore >= 5) return 'Low';
    return 'Very Low';
}

/**
 * Generate unique error ID for tracking
 * @returns {string} Error ID
 */
function generateErrorId() {
    return `err_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
}

/**
 * Get analysis statistics
 * @param {Object} event - Netlify event
 * @returns {Object} Statistics response
 */
export const getStats = async (event) => {
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        const textAnalyzerStats = textAnalyzer.getStatistics ? textAnalyzer.getStatistics() : {};
        const qualityValidatorStats = qualityValidator.getStatistics();

        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: '2.0.0',
                environment: 'netlify',
                statistics: {
                    textAnalyzer: textAnalyzerStats,
                    qualityValidator: qualityValidatorStats
                }
            })
        };
    } catch (error) {
        return {
            statusCode: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
                status: 'error',
                error: error.message,
                timestamp: new Date().toISOString()
            })
        };
    }
};
