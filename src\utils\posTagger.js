/**
 * Part-of-Speech Tagger
 * 
 * JavaScript implementation of POS tagging for text humanization.
 * Uses pattern matching and context analysis to identify word types
 * for targeted synonym replacement.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

/**
 * POS Tagger class for identifying parts of speech in text
 */
export class POSTagger {
    constructor() {
        this.initializePOSPatterns();
        this.initializeContextRules();
    }

    /**
     * Initialize POS pattern matching rules
     */
    initializePOSPatterns() {
        // Common word endings that indicate POS
        this.posPatterns = {
            // Adjectives
            'JJ': [
                /\w+ly$/i,      // quickly, slowly
                /\w+ful$/i,     // beautiful, helpful
                /\w+less$/i,    // helpless, careless
                /\w+ous$/i,     // famous, dangerous
                /\w+ive$/i,     // active, creative
                /\w+able$/i,    // readable, capable
                /\w+ible$/i,    // terrible, possible
                /\w+al$/i,      // natural, personal
                /\w+ic$/i,      // basic, specific
                /\w+ed$/i,      // interested, excited
                /\w+ing$/i      // interesting, exciting
            ],
            
            // Adverbs
            'RB': [
                /\w+ly$/i,      // quickly, slowly, really
                /\w+ward$/i,    // forward, backward
                /\w+wise$/i     // likewise, otherwise
            ],
            
            // Nouns
            'NN': [
                /\w+tion$/i,    // action, creation
                /\w+sion$/i,    // decision, conclusion
                /\w+ment$/i,    // development, agreement
                /\w+ness$/i,    // happiness, darkness
                /\w+ity$/i,     // quality, reality
                /\w+er$/i,      // teacher, worker
                /\w+or$/i,      // doctor, actor
                /\w+ist$/i,     // artist, scientist
                /\w+ism$/i      // capitalism, realism
            ],
            
            // Verbs
            'VB': [
                /\w+ize$/i,     // realize, organize
                /\w+ise$/i,     // organise, realise
                /\w+ify$/i,     // clarify, simplify
                /\w+ate$/i      // create, demonstrate
            ]
        };

        // Common function words
        this.functionWords = {
            'DT': ['the', 'a', 'an', 'this', 'that', 'these', 'those'],
            'IN': ['in', 'on', 'at', 'by', 'for', 'with', 'from', 'to', 'of', 'about'],
            'CC': ['and', 'or', 'but', 'so', 'yet', 'nor'],
            'PRP': ['i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'],
            'MD': ['can', 'could', 'may', 'might', 'must', 'shall', 'should', 'will', 'would']
        };

        // Common adjectives and adverbs for targeted replacement
        this.commonAdjectives = new Set([
            'good', 'great', 'bad', 'big', 'small', 'new', 'old', 'high', 'low', 'long', 'short',
            'important', 'significant', 'major', 'minor', 'large', 'little', 'different', 'same',
            'various', 'several', 'many', 'few', 'much', 'more', 'most', 'less', 'least',
            'excellent', 'outstanding', 'remarkable', 'notable', 'substantial', 'considerable'
        ]);

        this.commonAdverbs = new Set([
            'very', 'really', 'quite', 'rather', 'extremely', 'highly', 'particularly',
            'especially', 'significantly', 'considerably', 'substantially', 'remarkably',
            'notably', 'clearly', 'obviously', 'certainly', 'definitely', 'absolutely',
            'completely', 'entirely', 'fully', 'totally', 'perfectly', 'exactly'
        ]);
    }

    /**
     * Initialize context analysis rules
     */
    initializeContextRules() {
        this.contextRules = {
            // Words that often precede adjectives
            adjectivePrecursors: new Set([
                'very', 'quite', 'rather', 'extremely', 'highly', 'particularly',
                'especially', 'more', 'most', 'less', 'least', 'so', 'too'
            ]),
            
            // Words that often follow adjectives
            adjectiveFollowers: new Set([
                'than', 'as', 'enough', 'to'
            ]),
            
            // Sentence starters that indicate adverbs
            adverbStarters: new Set([
                'however', 'therefore', 'furthermore', 'moreover', 'additionally',
                'consequently', 'nevertheless', 'nonetheless', 'meanwhile'
            ])
        };
    }

    /**
     * Tokenize text and assign POS tags
     * @param {string} text - Input text
     * @returns {Promise<Array>} Array of token objects
     */
    async tokenizeAndTag(text) {
        // Basic tokenization
        const tokens = this.tokenize(text);
        const taggedTokens = [];

        for (let i = 0; i < tokens.length; i++) {
            const token = tokens[i];
            const context = this.getContext(tokens, i);
            
            const taggedToken = {
                word: token,
                pos: this.assignPOSTag(token, context),
                index: i,
                context: context,
                isProperNoun: this.isProperNoun(token, context),
                isTechnical: this.isTechnicalTerm(token),
                isCommonWord: this.isCommonWord(token)
            };

            taggedTokens.push(taggedToken);
        }

        return taggedTokens;
    }

    /**
     * Basic tokenization
     * @param {string} text - Input text
     * @returns {Array} Array of tokens
     */
    tokenize(text) {
        // Split on whitespace and punctuation, keeping punctuation
        return text.match(/\w+|[.,;:!?'"()[\]{}\-–—]/g) || [];
    }

    /**
     * Get context information for a token
     * @param {Array} tokens - All tokens
     * @param {number} index - Current token index
     * @returns {Object} Context information
     */
    getContext(tokens, index) {
        return {
            previous: index > 0 ? tokens[index - 1] : null,
            next: index < tokens.length - 1 ? tokens[index + 1] : null,
            previousTwo: index > 1 ? tokens[index - 2] : null,
            nextTwo: index < tokens.length - 2 ? tokens[index + 2] : null,
            position: index === 0 ? 'start' : index === tokens.length - 1 ? 'end' : 'middle'
        };
    }

    /**
     * Assign POS tag to a token
     * @param {string} token - Token to tag
     * @param {Object} context - Context information
     * @returns {string} POS tag
     */
    assignPOSTag(token, context) {
        const lowerToken = token.toLowerCase();

        // Check function words first
        for (const [pos, words] of Object.entries(this.functionWords)) {
            if (words.includes(lowerToken)) {
                return pos;
            }
        }

        // Check for punctuation
        if (/^[.,;:!?'"()[\]{}\-–—]$/.test(token)) {
            return 'PUNCT';
        }

        // Check for numbers
        if (/^\d+$/.test(token)) {
            return 'CD';
        }

        // Context-based tagging
        const contextTag = this.getContextBasedTag(token, context);
        if (contextTag) {
            return contextTag;
        }

        // Pattern-based tagging
        for (const [pos, patterns] of Object.entries(this.posPatterns)) {
            for (const pattern of patterns) {
                if (pattern.test(token)) {
                    return pos;
                }
            }
        }

        // Default classification based on common words
        if (this.commonAdjectives.has(lowerToken)) {
            return 'JJ';
        }
        
        if (this.commonAdverbs.has(lowerToken)) {
            return 'RB';
        }

        // Default to noun if nothing else matches
        return 'NN';
    }

    /**
     * Get POS tag based on context
     * @param {string} token - Token to analyze
     * @param {Object} context - Context information
     * @returns {string|null} Context-based POS tag
     */
    getContextBasedTag(token, context) {
        const lowerToken = token.toLowerCase();

        // Check for adverbs at sentence start
        if (context.position === 'start' && this.contextRules.adverbStarters.has(lowerToken)) {
            return 'RB';
        }

        // Check for adjectives after determiners or adverbs
        if (context.previous) {
            const prevLower = context.previous.toLowerCase();
            if (this.contextRules.adjectivePrecursors.has(prevLower)) {
                return 'JJ';
            }
        }

        // Check for adverbs ending in -ly
        if (token.endsWith('ly') && token.length > 3) {
            return 'RB';
        }

        return null;
    }

    /**
     * Check if token is a proper noun
     * @param {string} token - Token to check
     * @param {Object} context - Context information
     * @returns {boolean} Is proper noun
     */
    isProperNoun(token, context) {
        // Check capitalization (but not at sentence start)
        if (context.position !== 'start' && token[0] === token[0].toUpperCase()) {
            return true;
        }

        // Check for common proper noun patterns
        const properNounPatterns = [
            /^[A-Z][a-z]+$/,  // Capitalized words
            /^[A-Z]{2,}$/     // Acronyms
        ];

        return properNounPatterns.some(pattern => pattern.test(token));
    }

    /**
     * Check if token is a technical term
     * @param {string} token - Token to check
     * @returns {boolean} Is technical term
     */
    isTechnicalTerm(token) {
        // Common technical term patterns
        const technicalPatterns = [
            /^[a-z]+\d+$/i,     // version numbers, model numbers
            /^[A-Z]{3,}$/,      // Acronyms
            /\w+\.\w+/,         // Domain names, file extensions
            /^#\w+/,            // Hashtags
            /@\w+/              // Mentions
        ];

        return technicalPatterns.some(pattern => pattern.test(token));
    }

    /**
     * Check if token is a common word that should be prioritized for replacement
     * @param {string} token - Token to check
     * @returns {boolean} Is common word
     */
    isCommonWord(token) {
        const lowerToken = token.toLowerCase();
        return this.commonAdjectives.has(lowerToken) || this.commonAdverbs.has(lowerToken);
    }

    /**
     * Get detailed POS statistics for text
     * @param {string} text - Input text
     * @returns {Promise<Object>} POS statistics
     */
    async getPOSStatistics(text) {
        const tokens = await this.tokenizeAndTag(text);
        const stats = {};

        tokens.forEach(token => {
            stats[token.pos] = (stats[token.pos] || 0) + 1;
        });

        return {
            totalTokens: tokens.length,
            posDistribution: stats,
            replaceableTokens: tokens.filter(t => 
                ['JJ', 'JJR', 'JJS', 'RB', 'RBR', 'RBS'].includes(t.pos)
            ).length
        };
    }
}

// Export singleton instance
export const posTagger = new POSTagger();
