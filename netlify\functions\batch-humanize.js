/**
 * Netlify Function: Batch Text Humanization
 * 
 * Serverless function for processing multiple texts in a single request.
 * Optimized for premium and admin users with higher processing limits.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import { HumanizationService } from '../../src/services/humanizationService.js';

// Initialize humanization service
const humanizationService = new HumanizationService();

/**
 * Main Netlify function handler for batch processing
 * @param {Object} event - Netlify event object
 * @param {Object} context - Netlify context object
 * @returns {Promise<Object>} Response object
 */
export const handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Only allow POST requests
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Method not allowed. Use POST.'
            })
        };
    }

    try {
        // Parse request body
        let requestData;
        try {
            requestData = JSON.parse(event.body);
        } catch (parseError) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Invalid JSON in request body'
                })
            };
        }

        // Validate required fields
        const { texts, options = {} } = requestData;
        
        if (!texts || !Array.isArray(texts)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Texts field is required and must be an array'
                })
            };
        }

        // Extract user tier
        const userTier = extractUserTier(event.headers, options);
        
        // Check batch processing permissions
        const batchLimits = getBatchLimits(userTier);
        if (texts.length > batchLimits.maxTexts) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: `Batch size exceeds limit. Maximum ${batchLimits.maxTexts} texts allowed for ${userTier} tier.`
                })
            };
        }

        // Validate individual texts
        const validationResult = validateTexts(texts, batchLimits);
        if (!validationResult.valid) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: validationResult.error
                })
            };
        }

        // Rate limiting check
        const rateLimitResult = await checkBatchRateLimit(event, userTier, texts.length);
        if (!rateLimitResult.allowed) {
            return {
                statusCode: 429,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Batch rate limit exceeded. Please try again later.',
                    retryAfter: rateLimitResult.retryAfter
                })
            };
        }

        // Process texts in batches
        const startTime = Date.now();
        const results = await processBatch(texts, options, userTier, batchLimits);
        const totalProcessingTime = Date.now() - startTime;

        // Calculate batch statistics
        const batchStats = calculateBatchStats(results);

        // Prepare response
        const response = {
            success: true,
            batchSize: texts.length,
            results,
            statistics: batchStats,
            metadata: {
                functionVersion: '2.0.0',
                processingTime: totalProcessingTime,
                userTier,
                timestamp: new Date().toISOString(),
                serverless: true,
                environment: 'netlify'
            }
        };

        // Log batch usage
        await logBatchUsage({
            userTier,
            batchSize: texts.length,
            processingTime: totalProcessingTime,
            successCount: batchStats.successCount,
            timestamp: new Date().toISOString()
        });

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(response)
        };

    } catch (error) {
        console.error('Batch humanization function error:', error);
        
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Internal server error during batch processing. Please try again later.',
                errorId: generateErrorId()
            })
        };
    }
};

/**
 * Extract user tier from request
 * @param {Object} headers - Request headers
 * @param {Object} options - Request options
 * @returns {string} User tier
 */
function extractUserTier(headers, options) {
    // Check for tier in options first
    if (options.userTier && ['freemium', 'premium', 'admin'].includes(options.userTier)) {
        return options.userTier;
    }

    // Check authorization header
    const authHeader = headers.authorization || headers.Authorization;
    if (authHeader) {
        if (authHeader.includes('admin')) return 'admin';
        if (authHeader.includes('premium')) return 'premium';
    }

    // Check for API key
    const apiKey = headers['x-api-key'] || headers['X-API-Key'];
    if (apiKey) {
        return 'premium';
    }

    return 'freemium';
}

/**
 * Get batch processing limits for user tier
 * @param {string} userTier - User tier
 * @returns {Object} Batch limits
 */
function getBatchLimits(userTier) {
    const limits = {
        freemium: {
            maxTexts: 5,
            maxTextLength: 5000,
            maxTotalLength: 20000,
            concurrency: 2
        },
        premium: {
            maxTexts: 25,
            maxTextLength: 20000,
            maxTotalLength: 200000,
            concurrency: 5
        },
        admin: {
            maxTexts: 100,
            maxTextLength: 50000,
            maxTotalLength: 1000000,
            concurrency: 10
        }
    };

    return limits[userTier] || limits.freemium;
}

/**
 * Validate array of texts
 * @param {Array} texts - Array of texts to validate
 * @param {Object} limits - Batch limits
 * @returns {Object} Validation result
 */
function validateTexts(texts, limits) {
    let totalLength = 0;

    for (let i = 0; i < texts.length; i++) {
        const text = texts[i];
        
        if (!text || typeof text !== 'string') {
            return {
                valid: false,
                error: `Text at index ${i} is invalid. Must be a non-empty string.`
            };
        }

        if (text.length > limits.maxTextLength) {
            return {
                valid: false,
                error: `Text at index ${i} exceeds maximum length of ${limits.maxTextLength} characters.`
            };
        }

        if (text.length < 10) {
            return {
                valid: false,
                error: `Text at index ${i} is too short. Minimum 10 characters required.`
            };
        }

        totalLength += text.length;
    }

    if (totalLength > limits.maxTotalLength) {
        return {
            valid: false,
            error: `Total batch length (${totalLength}) exceeds maximum of ${limits.maxTotalLength} characters.`
        };
    }

    return { valid: true };
}

/**
 * Check batch rate limiting
 * @param {Object} event - Netlify event
 * @param {string} userTier - User tier
 * @param {number} batchSize - Number of texts in batch
 * @returns {Promise<Object>} Rate limit result
 */
async function checkBatchRateLimit(event, userTier, batchSize) {
    // Simplified rate limiting for serverless
    // In production, implement proper distributed rate limiting
    
    const batchLimits = {
        freemium: { batches: 5, window: 300000 }, // 5 batches per 5 minutes
        premium: { batches: 50, window: 300000 }, // 50 batches per 5 minutes
        admin: { batches: 500, window: 300000 }   // 500 batches per 5 minutes
    };

    const limit = batchLimits[userTier] || batchLimits.freemium;
    
    // For serverless, allow all requests for now
    return {
        allowed: true,
        remaining: limit.batches,
        resetTime: Date.now() + limit.window
    };
}

/**
 * Process batch of texts
 * @param {Array} texts - Array of texts to process
 * @param {Object} options - Processing options
 * @param {string} userTier - User tier
 * @param {Object} limits - Batch limits
 * @returns {Promise<Array>} Processing results
 */
async function processBatch(texts, options, userTier, limits) {
    const results = [];
    const concurrency = limits.concurrency;
    
    // Process texts in chunks to respect concurrency limits
    for (let i = 0; i < texts.length; i += concurrency) {
        const chunk = texts.slice(i, i + concurrency);
        
        // Process chunk in parallel
        const chunkPromises = chunk.map(async (text, index) => {
            const textIndex = i + index;
            
            try {
                const humanizationOptions = {
                    userTier,
                    targetDetection: options.targetDetection || 10,
                    maintainTone: options.maintainTone !== false,
                    preserveFormatting: options.preserveFormatting !== false,
                    aggressiveness: Math.min(Math.max(options.aggressiveness || 0.7, 0.1), 1.0),
                    ...options
                };

                const result = await humanizationService.humanizeText(text, humanizationOptions);
                
                return {
                    index: textIndex,
                    success: result.success,
                    originalText: text,
                    humanizedText: result.humanizedText,
                    transformationStats: result.transformationStats,
                    qualityMetrics: result.qualityMetrics,
                    processingTime: result.processingTime,
                    error: result.error
                };
                
            } catch (error) {
                return {
                    index: textIndex,
                    success: false,
                    originalText: text,
                    error: error.message,
                    processingTime: 0
                };
            }
        });

        // Wait for chunk to complete
        const chunkResults = await Promise.all(chunkPromises);
        results.push(...chunkResults);
        
        // Small delay between chunks to prevent overwhelming the system
        if (i + concurrency < texts.length) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    return results;
}

/**
 * Calculate batch processing statistics
 * @param {Array} results - Processing results
 * @returns {Object} Batch statistics
 */
function calculateBatchStats(results) {
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    const totalProcessingTime = results.reduce((sum, r) => sum + (r.processingTime || 0), 0);
    const averageProcessingTime = totalProcessingTime / results.length;
    
    // Calculate transformation statistics for successful results
    const successfulResults = results.filter(r => r.success && r.transformationStats);
    const averageTransformationRate = successfulResults.length > 0 
        ? successfulResults.reduce((sum, r) => sum + r.transformationStats.transformationRate, 0) / successfulResults.length
        : 0;

    // Calculate quality statistics
    const qualityResults = results.filter(r => r.success && r.qualityMetrics);
    const averageAIDetection = qualityResults.length > 0
        ? qualityResults.reduce((sum, r) => sum + r.qualityMetrics.aiDetectionScore, 0) / qualityResults.length
        : 0;

    return {
        totalTexts: results.length,
        successCount,
        failureCount,
        successRate: Math.round((successCount / results.length) * 100),
        totalProcessingTime,
        averageProcessingTime: Math.round(averageProcessingTime),
        averageTransformationRate: Math.round(averageTransformationRate * 10) / 10,
        averageAIDetection: Math.round(averageAIDetection * 10) / 10
    };
}

/**
 * Log batch usage for analytics
 * @param {Object} usageData - Usage data
 * @returns {Promise<void>}
 */
async function logBatchUsage(usageData) {
    try {
        console.log('Batch usage logged:', {
            userTier: usageData.userTier,
            batchSize: usageData.batchSize,
            processingTime: usageData.processingTime,
            successCount: usageData.successCount,
            timestamp: usageData.timestamp
        });
    } catch (error) {
        console.error('Failed to log batch usage:', error);
    }
}

/**
 * Generate unique error ID for tracking
 * @returns {string} Error ID
 */
function generateErrorId() {
    return `batch_err_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
}
