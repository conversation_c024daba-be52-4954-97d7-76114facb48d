/**
 * Hesitation Marker Injector
 * 
 * Strategic injection of natural hesitation markers and human-like elements
 * to reduce AI detection while maintaining professional tone. Implements
 * context-aware placement with maximum 5% frequency limit.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

/**
 * Hesitation Marker Injector class for natural human elements
 */
export class HesitationMarkerInjector {
    constructor() {
        this.initializeHesitationMarkers();
        this.initializeContextRules();
        this.initializePlacementStrategies();
    }

    /**
     * Initialize hesitation markers and human elements
     */
    initializeHesitationMarkers() {
        // Different types of hesitation markers
        this.hesitationTypes = {
            // Subtle qualifiers (professional contexts)
            subtle: {
                markers: [
                    'perhaps', 'possibly', 'potentially', 'likely', 'probably',
                    'it seems', 'it appears', 'one might say', 'in many cases',
                    'generally speaking', 'to some extent', 'in most instances'
                ],
                weight: 0.6,
                contexts: ['formal', 'academic', 'professional']
            },

            // Mild uncertainty (balanced contexts)
            mild: {
                markers: [
                    'I think', 'I believe', 'I feel', 'it looks like',
                    'from what I can see', 'as far as I know', 'in my view',
                    'it would seem', 'one could argue', 'arguably'
                ],
                weight: 0.4,
                contexts: ['balanced', 'conversational']
            },

            // Casual hesitations (informal contexts)
            casual: {
                markers: [
                    'well', 'you know', 'I mean', 'sort of', 'kind of',
                    'basically', 'actually', 'honestly', 'frankly',
                    'to be honest', 'if you ask me'
                ],
                weight: 0.3,
                contexts: ['casual', 'informal', 'conversational']
            },

            // Transitional softeners
            transitional: {
                markers: [
                    'that said', 'having said that', 'mind you', 'then again',
                    'on second thought', 'come to think of it', 'all things considered',
                    'by the way', 'incidentally', 'as it happens'
                ],
                weight: 0.5,
                contexts: ['all']
            },

            // Emphasis modifiers
            emphasis: {
                markers: [
                    'quite frankly', 'to be fair', 'in all honesty',
                    'truth be told', 'let\'s face it', 'the fact is',
                    'what\'s interesting is', 'notably', 'remarkably'
                ],
                weight: 0.4,
                contexts: ['balanced', 'professional']
            }
        };

        // Sentence position preferences for each type
        this.positionPreferences = {
            subtle: ['beginning', 'middle'],
            mild: ['beginning', 'middle'],
            casual: ['beginning', 'middle', 'end'],
            transitional: ['beginning'],
            emphasis: ['beginning', 'middle']
        };
    }

    /**
     * Initialize context-aware placement rules
     */
    initializeContextRules() {
        this.contextRules = {
            // Forbidden contexts (never place hesitation markers here)
            forbidden: {
                headings: /^#{1,6}\s+/,
                titles: /^[A-Z][^.!?]*$/,
                lists: /^\s*[-*+]\s+/,
                numbers: /^\d+\.\s+/,
                quotes: /^["']/,
                code: /`[^`]*`/,
                urls: /https?:\/\/[^\s]+/,
                emails: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/
            },

            // Preferred contexts (good places for hesitation markers)
            preferred: {
                opinions: /\b(think|believe|feel|opinion|view|perspective)\b/i,
                uncertainty: /\b(might|could|may|possibly|perhaps|maybe)\b/i,
                explanations: /\b(because|since|due to|as a result)\b/i,
                comparisons: /\b(compared to|versus|rather than|instead of)\b/i,
                conclusions: /\b(therefore|thus|consequently|in conclusion)\b/i
            },

            // Sentence types that benefit from hesitation markers
            beneficialSentences: {
                statements: /^[A-Z][^.!?]*\./,
                explanations: /\b(this means|this suggests|this indicates)\b/i,
                observations: /\b(notice|observe|see|find|discover)\b/i,
                recommendations: /\b(should|recommend|suggest|advise)\b/i
            }
        };

        // Context detection patterns
        this.contextDetection = {
            formal: /\b(furthermore|moreover|consequently|therefore|however)\b/i,
            academic: /\b(research|study|analysis|methodology|findings)\b/i,
            technical: /\b(system|process|implementation|algorithm|framework)\b/i,
            casual: /\b(really|pretty|quite|sort of|kind of)\b/i,
            conversational: /\b(you know|I mean|well|actually|honestly)\b/i
        };
    }

    /**
     * Initialize placement strategies
     */
    initializePlacementStrategies() {
        this.placementStrategies = {
            // Distribute evenly throughout text
            even: {
                name: 'Even Distribution',
                calculate: (sentenceCount, maxFrequency) => {
                    const maxMarkers = Math.floor(sentenceCount * maxFrequency);
                    const interval = Math.floor(sentenceCount / maxMarkers);
                    return Array.from({ length: maxMarkers }, (_, i) => i * interval);
                }
            },

            // Focus on middle sections
            middle: {
                name: 'Middle Focus',
                calculate: (sentenceCount, maxFrequency) => {
                    const maxMarkers = Math.floor(sentenceCount * maxFrequency);
                    const start = Math.floor(sentenceCount * 0.2);
                    const end = Math.floor(sentenceCount * 0.8);
                    const range = end - start;
                    const interval = Math.floor(range / maxMarkers);
                    return Array.from({ length: maxMarkers }, (_, i) => start + (i * interval));
                }
            },

            // Random placement with clustering avoidance
            random: {
                name: 'Random Placement',
                calculate: (sentenceCount, maxFrequency) => {
                    const maxMarkers = Math.floor(sentenceCount * maxFrequency);
                    const positions = [];
                    const minDistance = Math.max(2, Math.floor(sentenceCount / maxMarkers / 2));
                    
                    while (positions.length < maxMarkers) {
                        const pos = Math.floor(Math.random() * sentenceCount);
                        const tooClose = positions.some(existing => Math.abs(existing - pos) < minDistance);
                        if (!tooClose) {
                            positions.push(pos);
                        }
                    }
                    
                    return positions.sort((a, b) => a - b);
                }
            }
        };
    }

    /**
     * Add hesitation markers to text
     * @param {string} text - Input text
     * @param {Object} options - Configuration options
     * @returns {Promise<Object>} Result with modified text
     */
    async addHesitationMarkers(text, options = {}) {
        const config = {
            maxFrequency: 0.05, // Maximum 5% of sentences
            contextAware: true,
            strategy: 'random',
            toneContext: 'balanced',
            preserveFormatting: true,
            ...options
        };

        try {
            // Split text into sentences
            const sentences = this.splitIntoSentences(text);
            
            if (sentences.length === 0) {
                return { text, markersAdded: 0, success: true };
            }

            // Detect overall context
            const detectedContext = this.detectTextContext(text);
            const effectiveContext = config.toneContext === 'auto' ? detectedContext : config.toneContext;

            // Calculate placement positions
            const placementPositions = this.calculatePlacementPositions(
                sentences.length, 
                config.maxFrequency, 
                config.strategy
            );

            // Process sentences and add markers
            let processedSentences = [...sentences];
            let markersAdded = 0;

            for (const position of placementPositions) {
                if (position >= sentences.length) continue;

                const sentence = sentences[position];
                
                // Check if this sentence is suitable for hesitation markers
                if (!this.isSuitableForHesitation(sentence, config)) {
                    continue;
                }

                // Select appropriate hesitation marker
                const marker = this.selectHesitationMarker(sentence, effectiveContext);
                
                if (marker) {
                    // Apply the marker to the sentence
                    const modifiedSentence = this.applyHesitationMarker(sentence, marker);
                    
                    if (modifiedSentence !== sentence) {
                        processedSentences[position] = modifiedSentence;
                        markersAdded++;
                    }
                }
            }

            // Reconstruct text
            const resultText = processedSentences.join(' ');

            return {
                success: true,
                text: resultText,
                markersAdded,
                totalSentences: sentences.length,
                frequency: Math.round((markersAdded / sentences.length) * 1000) / 10, // Percentage with 1 decimal
                context: effectiveContext,
                strategy: config.strategy
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                text,
                markersAdded: 0
            };
        }
    }

    /**
     * Split text into sentences
     * @param {string} text - Input text
     * @returns {Array} Array of sentences
     */
    splitIntoSentences(text) {
        // More sophisticated sentence splitting that preserves formatting
        return text.split(/(?<=[.!?])\s+/).filter(s => s.trim().length > 0);
    }

    /**
     * Detect the overall context/tone of the text
     * @param {string} text - Input text
     * @returns {string} Detected context
     */
    detectTextContext(text) {
        const contextScores = {};
        
        for (const [context, pattern] of Object.entries(this.contextDetection)) {
            const matches = (text.match(pattern) || []).length;
            contextScores[context] = matches;
        }

        // Find the context with the highest score
        const maxScore = Math.max(...Object.values(contextScores));
        if (maxScore === 0) return 'balanced';

        const detectedContext = Object.keys(contextScores).find(
            context => contextScores[context] === maxScore
        );

        return detectedContext || 'balanced';
    }

    /**
     * Calculate placement positions for hesitation markers
     * @param {number} sentenceCount - Total number of sentences
     * @param {number} maxFrequency - Maximum frequency (0-1)
     * @param {string} strategy - Placement strategy
     * @returns {Array} Array of sentence positions
     */
    calculatePlacementPositions(sentenceCount, maxFrequency, strategy) {
        const strategyFunc = this.placementStrategies[strategy] || this.placementStrategies.random;
        return strategyFunc.calculate(sentenceCount, maxFrequency);
    }

    /**
     * Check if a sentence is suitable for hesitation markers
     * @param {string} sentence - Sentence to check
     * @param {Object} config - Configuration
     * @returns {boolean} Is suitable
     */
    isSuitableForHesitation(sentence, config) {
        // Check forbidden contexts
        for (const [context, pattern] of Object.entries(this.contextRules.forbidden)) {
            if (pattern.test(sentence)) {
                return false;
            }
        }

        // Must be long enough
        if (sentence.length < 20) {
            return false;
        }

        // Must not already have hesitation markers
        const existingMarkers = Object.values(this.hesitationTypes)
            .flatMap(type => type.markers)
            .some(marker => sentence.toLowerCase().includes(marker.toLowerCase()));
        
        if (existingMarkers) {
            return false;
        }

        return true;
    }

    /**
     * Select appropriate hesitation marker for a sentence
     * @param {string} sentence - Target sentence
     * @param {string} context - Text context
     * @returns {Object|null} Selected marker info
     */
    selectHesitationMarker(sentence, context) {
        // Find suitable hesitation types for this context
        const suitableTypes = Object.entries(this.hesitationTypes).filter(
            ([type, data]) => data.contexts.includes(context) || data.contexts.includes('all')
        );

        if (suitableTypes.length === 0) {
            return null;
        }

        // Weight selection based on sentence characteristics
        const weights = suitableTypes.map(([type, data]) => {
            let weight = data.weight;
            
            // Boost weight for preferred contexts
            for (const [prefContext, pattern] of Object.entries(this.contextRules.preferred)) {
                if (pattern.test(sentence)) {
                    weight *= 1.5;
                    break;
                }
            }
            
            return weight;
        });

        // Select type based on weighted random
        const totalWeight = weights.reduce((sum, w) => sum + w, 0);
        let random = Math.random() * totalWeight;
        
        for (let i = 0; i < suitableTypes.length; i++) {
            random -= weights[i];
            if (random <= 0) {
                const [selectedType, typeData] = suitableTypes[i];
                const markers = typeData.markers;
                const selectedMarker = markers[Math.floor(Math.random() * markers.length)];
                
                return {
                    type: selectedType,
                    marker: selectedMarker,
                    positions: this.positionPreferences[selectedType] || ['beginning']
                };
            }
        }

        return null;
    }

    /**
     * Apply hesitation marker to a sentence
     * @param {string} sentence - Original sentence
     * @param {Object} markerInfo - Marker information
     * @returns {string} Modified sentence
     */
    applyHesitationMarker(sentence, markerInfo) {
        const { marker, positions } = markerInfo;
        const position = positions[Math.floor(Math.random() * positions.length)];

        switch (position) {
            case 'beginning':
                return this.addToBeginning(sentence, marker);
            
            case 'middle':
                return this.addToMiddle(sentence, marker);
            
            case 'end':
                return this.addToEnd(sentence, marker);
            
            default:
                return this.addToBeginning(sentence, marker);
        }
    }

    /**
     * Add marker to beginning of sentence
     * @param {string} sentence - Original sentence
     * @param {string} marker - Hesitation marker
     * @returns {string} Modified sentence
     */
    addToBeginning(sentence, marker) {
        const capitalizedMarker = marker.charAt(0).toUpperCase() + marker.slice(1);
        return `${capitalizedMarker}, ${sentence.charAt(0).toLowerCase()}${sentence.slice(1)}`;
    }

    /**
     * Add marker to middle of sentence
     * @param {string} sentence - Original sentence
     * @param {string} marker - Hesitation marker
     * @returns {string} Modified sentence
     */
    addToMiddle(sentence, marker) {
        const words = sentence.split(' ');
        if (words.length < 6) return this.addToBeginning(sentence, marker);

        // Find a good insertion point (after a comma or conjunction)
        let insertIndex = Math.floor(words.length / 2);
        
        // Look for natural break points
        for (let i = 2; i < words.length - 2; i++) {
            if (words[i].endsWith(',') || ['and', 'but', 'or', 'so'].includes(words[i].toLowerCase())) {
                insertIndex = i + 1;
                break;
            }
        }

        words.splice(insertIndex, 0, `${marker},`);
        return words.join(' ');
    }

    /**
     * Add marker to end of sentence
     * @param {string} sentence - Original sentence
     * @param {string} marker - Hesitation marker
     * @returns {string} Modified sentence
     */
    addToEnd(sentence, marker) {
        const lastChar = sentence.slice(-1);
        const mainSentence = sentence.slice(0, -1);
        return `${mainSentence}, ${marker}${lastChar}`;
    }

    /**
     * Get statistics about hesitation markers
     * @returns {Object} Statistics
     */
    getStatistics() {
        const totalMarkers = Object.values(this.hesitationTypes)
            .reduce((sum, type) => sum + type.markers.length, 0);

        return {
            totalMarkerTypes: Object.keys(this.hesitationTypes).length,
            totalMarkers,
            averageMarkersPerType: Math.round(totalMarkers / Object.keys(this.hesitationTypes).length),
            availableStrategies: Object.keys(this.placementStrategies).length,
            contextTypes: Object.keys(this.contextDetection).length
        };
    }

    /**
     * Validate hesitation marker frequency
     * @param {string} text - Text to validate
     * @returns {Object} Validation result
     */
    validateFrequency(text) {
        const sentences = this.splitIntoSentences(text);
        const allMarkers = Object.values(this.hesitationTypes)
            .flatMap(type => type.markers);

        let markerCount = 0;
        for (const marker of allMarkers) {
            const regex = new RegExp(`\\b${marker.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
            markerCount += (text.match(regex) || []).length;
        }

        const frequency = markerCount / sentences.length;
        
        return {
            markerCount,
            sentenceCount: sentences.length,
            frequency: Math.round(frequency * 1000) / 10, // Percentage with 1 decimal
            withinLimit: frequency <= 0.05,
            recommendation: frequency > 0.05 ? 'Reduce hesitation markers' : 'Frequency is appropriate'
        };
    }
}

// Export singleton instance
export const hesitationMarkerInjector = new HesitationMarkerInjector();
