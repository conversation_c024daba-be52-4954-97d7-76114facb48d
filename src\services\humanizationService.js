/**
 * Advanced Text Humanization Service
 * 
 * This service implements a comprehensive text humanization algorithm designed to achieve
 * ≤10% AI detection scores on ZeroGPT, Originality.ai, and GPTZero while maintaining
 * professional tone, proper grammar, and exact paragraph structure.
 * 
 * Key Techniques:
 * - Advanced synonym replacement for adjectives and adverbs
 * - Sentence structure variation and reordering
 * - Contextual word replacement with semantic preservation
 * - Strategic hesitation marker placement (≤5% frequency)
 * - Minimum 80% word replacement with substantial structural changes
 * - Professional tone maintenance with proper grammar
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import { POSTagger } from '../utils/posTagger.js';
import { SynonymDatabase } from '../utils/synonymDatabase.js';
import { SentenceTransformer } from '../utils/sentenceTransformer.js';
import { TextAnalyzer } from '../utils/textAnalyzer.js';
import { HesitationMarkerInjector } from '../utils/hesitationMarkers.js';
import { QualityValidator } from '../utils/qualityValidator.js';
import { tieredPerformanceManager } from './tieredPerformanceManager.js';

/**
 * Main humanization service class
 */
export class HumanizationService {
    constructor() {
        this.posTagger = new POSTagger();
        this.synonymDb = new SynonymDatabase();
        this.sentenceTransformer = new SentenceTransformer();
        this.textAnalyzer = new TextAnalyzer();
        this.hesitationInjector = new HesitationMarkerInjector();
        this.qualityValidator = new QualityValidator();
        this.performanceManager = tieredPerformanceManager;
    }

    /**
     * Main humanization method
     * @param {string} text - Input text to humanize
     * @param {Object} options - Humanization options
     * @returns {Promise<Object>} Humanization result
     */
    async humanizeText(text, options = {}) {
        const startTime = Date.now();

        try {
            // Validate input
            if (!text || typeof text !== 'string') {
                throw new Error('Invalid input text');
            }

            // Set default options
            const config = {
                userTier: 'freemium',
                targetDetection: 10,
                maintainTone: true,
                preserveFormatting: true,
                aggressiveness: 0.7,
                ...options
            };

            // Validate request against tier limits
            const validation = this.performanceManager.validateRequest(config.userTier, {
                textLength: text.length,
                features: this.extractRequestedFeatures(config)
            });

            if (!validation.valid) {
                throw new Error(`Tier validation failed: ${validation.errors.join(', ')}`);
            }

            // Get optimized processing configuration
            const processingConfig = this.performanceManager.getProcessingConfig(config.userTier, config);
            
            // Analyze original text
            const analysis = await this.textAnalyzer.analyzeText(text);
            
            // Perform multi-pass humanization
            let humanizedText = text;
            let transformationStats = {
                originalLength: text.length,
                passes: 0,
                wordsReplaced: 0,
                sentencesTransformed: 0,
                hesitationMarkersAdded: 0
            };

            for (let pass = 0; pass < processingConfig.maxPasses; pass++) {
                const passResult = await this.performHumanizationPass(
                    humanizedText,
                    processingConfig,
                    pass + 1
                );
                
                humanizedText = passResult.text;
                transformationStats.passes++;
                transformationStats.wordsReplaced += passResult.wordsReplaced;
                transformationStats.sentencesTransformed += passResult.sentencesTransformed;
                transformationStats.hesitationMarkersAdded += passResult.hesitationMarkersAdded;

                // Check if target quality is achieved
                const qualityCheck = await this.qualityValidator.validateQuality(
                    humanizedText,
                    processingConfig.targetDetection
                );

                if (qualityCheck.aiDetectionScore <= processingConfig.targetDetection) {
                    break;
                }
            }

            // Final quality validation
            const finalValidation = await this.qualityValidator.validateQuality(
                humanizedText,
                processingConfig.targetDetection
            );

            // Calculate transformation rate
            const transformationRate = Math.round(
                (transformationStats.wordsReplaced / analysis.wordCount) * 100
            );

            return {
                success: true,
                originalText: text,
                humanizedText,
                processingTime: Date.now() - startTime,
                transformationStats: {
                    ...transformationStats,
                    transformationRate,
                    finalLength: humanizedText.length
                },
                qualityMetrics: {
                    aiDetectionScore: finalValidation.aiDetectionScore,
                    grammarScore: finalValidation.grammarScore,
                    readabilityScore: finalValidation.readabilityScore,
                    toneConsistency: finalValidation.toneConsistency
                },
                userTier: processingConfig.userTier,
                processingConfig: processingConfig
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                processingTime: Date.now() - startTime,
                originalText: text
            };
        }
    }

    /**
     * Extract requested features from configuration
     * @param {Object} config - Configuration options
     * @returns {Array} Array of requested features
     */
    extractRequestedFeatures(config) {
        const features = [];

        if (config.enableAdvancedSynonyms) features.push('advancedSynonyms');
        if (config.enableSentenceTransformation) features.push('sentenceTransformation');
        if (config.enableHesitationMarkers) features.push('hesitationMarkers');
        if (config.enableQualityValidation) features.push('qualityValidation');
        if (config.enableBatchProcessing) features.push('batchProcessing');

        return features;
    }

    /**
     * Perform a single humanization pass
     * @param {string} text - Text to process
     * @param {Object} processingConfig - Processing configuration
     * @param {number} passNumber - Current pass number
     * @returns {Promise<Object>} Pass result
     */
    async performHumanizationPass(text, processingConfig, passNumber) {
        let processedText = text;
        let stats = {
            wordsReplaced: 0,
            sentencesTransformed: 0,
            hesitationMarkersAdded: 0
        };

        // Step 1: POS tagging and synonym replacement
        const synonymResult = await this.performSynonymReplacement(
            processedText,
            processingConfig.aggressiveness,
            processingConfig
        );
        processedText = synonymResult.text;
        stats.wordsReplaced += synonymResult.replacements;

        // Step 2: Sentence structure transformation (if enabled)
        if (processingConfig.enableSentenceTransformation) {
            const structureResult = await this.sentenceTransformer.transformSentences(
                processedText,
                {
                    aggressiveness: processingConfig.aggressiveness,
                    maintainTone: processingConfig.maintainTone,
                    passNumber
                }
            );
            processedText = structureResult.text;
            stats.sentencesTransformed += structureResult.transformations;
        }

        // Step 3: Add hesitation markers (only on final pass and if enabled)
        if (passNumber === processingConfig.maxPasses && processingConfig.enableHesitationMarkers) {
            const hesitationResult = await this.hesitationInjector.addHesitationMarkers(
                processedText,
                { maxFrequency: 0.05, contextAware: true }
            );
            processedText = hesitationResult.text;
            stats.hesitationMarkersAdded += hesitationResult.markersAdded;
        }

        return {
            text: processedText,
            ...stats
        };
    }

    /**
     * Perform advanced synonym replacement
     * @param {string} text - Input text
     * @param {number} aggressiveness - Replacement aggressiveness (0-1)
     * @param {Object} processingConfig - Processing configuration
     * @returns {Promise<Object>} Replacement result
     */
    async performSynonymReplacement(text, aggressiveness, processingConfig) {
        // Tokenize and tag parts of speech
        const tokens = await this.posTagger.tokenizeAndTag(text);
        let replacements = 0;
        let processedTokens = [];

        for (const token of tokens) {
            if (this.shouldReplaceToken(token, aggressiveness, processingConfig)) {
                const synonym = await this.synonymDb.getBestSynonym(
                    token.word,
                    token.pos,
                    token.context
                );

                if (synonym && synonym !== token.word) {
                    processedTokens.push({
                        ...token,
                        word: this.preserveCapitalization(token.word, synonym)
                    });
                    replacements++;
                } else {
                    processedTokens.push(token);
                }
            } else {
                processedTokens.push(token);
            }
        }

        // Reconstruct text
        const reconstructedText = this.reconstructTextFromTokens(processedTokens);

        return {
            text: reconstructedText,
            replacements
        };
    }

    /**
     * Determine if a token should be replaced
     * @param {Object} token - Token object
     * @param {number} aggressiveness - Replacement aggressiveness
     * @param {Object} processingConfig - Processing configuration
     * @returns {boolean} Should replace
     */
    shouldReplaceToken(token, aggressiveness, processingConfig) {
        // Target adjectives and adverbs primarily
        const targetPOS = ['JJ', 'JJR', 'JJS', 'RB', 'RBR', 'RBS'];
        
        if (!targetPOS.includes(token.pos)) {
            return false;
        }

        // Skip very short words
        if (token.word.length < 3) {
            return false;
        }

        // Skip proper nouns and technical terms
        if (token.isProperNoun || token.isTechnical) {
            return false;
        }

        // Use aggressiveness to determine replacement probability
        // Advanced synonyms may have different probability based on tier
        const baseAggressiveness = processingConfig.enableAdvancedSynonyms ? aggressiveness * 1.2 : aggressiveness;
        return Math.random() < Math.min(baseAggressiveness, 1.0);
    }

    /**
     * Preserve original capitalization when replacing words
     * @param {string} original - Original word
     * @param {string} replacement - Replacement word
     * @returns {string} Properly capitalized replacement
     */
    preserveCapitalization(original, replacement) {
        if (original[0] === original[0].toUpperCase()) {
            return replacement.charAt(0).toUpperCase() + replacement.slice(1).toLowerCase();
        }
        return replacement.toLowerCase();
    }

    /**
     * Reconstruct text from processed tokens
     * @param {Array} tokens - Processed tokens
     * @returns {string} Reconstructed text
     */
    reconstructTextFromTokens(tokens) {
        let result = '';
        
        for (let i = 0; i < tokens.length; i++) {
            const token = tokens[i];
            
            // Add the word
            result += token.word;
            
            // Add appropriate spacing
            if (i < tokens.length - 1) {
                const nextToken = tokens[i + 1];
                if (!this.isPunctuation(nextToken.word)) {
                    result += ' ';
                }
            }
        }
        
        return result;
    }

    /**
     * Check if a word is punctuation
     * @param {string} word - Word to check
     * @returns {boolean} Is punctuation
     */
    isPunctuation(word) {
        return /^[.,;:!?'"()[\]{}\-–—]$/.test(word);
    }

    /**
     * Get service status and capabilities
     * @returns {Object} Service status
     */
    getServiceStatus() {
        return {
            available: true,
            version: '2.0.0',
            capabilities: {
                synonymReplacement: true,
                sentenceTransformation: true,
                hesitationMarkers: true,
                qualityValidation: true,
                tieredPerformance: true,
                batchProcessing: true
            },
            supportedTiers: this.performanceManager.getAllTiers().map(tier => ({
                name: tier.tier,
                displayName: tier.displayName,
                features: Object.keys(tier.features).filter(feature => tier.features[feature])
            })),
            performanceManager: {
                version: this.performanceManager.constructor.name,
                totalTiers: this.performanceManager.getAllTiers().length
            }
        };
    }

    /**
     * Get tier information for a user
     * @param {string} userTier - User tier
     * @returns {Object} Tier information
     */
    getTierInfo(userTier) {
        return this.performanceManager.getUsageStats(userTier);
    }

    /**
     * Get tier comparison for upgrade recommendations
     * @param {string} currentTier - Current user tier
     * @returns {Object} Tier comparison
     */
    getTierComparison(currentTier) {
        return this.performanceManager.getTierComparison(currentTier);
    }
}

// Export singleton instance
export const humanizationService = new HumanizationService();

// Export main function for backward compatibility
export async function humanizeText(text, options = {}) {
    return await humanizationService.humanizeText(text, options);
}
