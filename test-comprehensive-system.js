/**
 * Comprehensive System Test Suite
 * 
 * Tests the complete GhostLayer humanization system including:
 * - Core humanization service
 * - Tiered performance management
 * - Quality validation
 * - Netlify function compatibility
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import { HumanizationService } from './src/services/humanizationService.js';
import { TieredPerformanceManager } from './src/services/tieredPerformanceManager.js';
import { TextAnalyzer } from './src/utils/textAnalyzer.js';
import { QualityValidator } from './src/utils/qualityValidator.js';

// Test data with varying AI detection levels
const testCases = [
    {
        name: "High AI Detection Text",
        text: "Furthermore, artificial intelligence demonstrates significant potential for revolutionizing numerous industries. Additionally, these sophisticated systems utilize advanced algorithms to effectively analyze vast amounts of data. Moreover, the comprehensive implementation of machine learning technologies has resulted in substantial improvements across various sectors.",
        expectedDetection: "high",
        targetTier: "premium"
    },
    {
        name: "Medium AI Detection Text", 
        text: "The development of new technologies has changed how we work. Companies are using these tools to improve their processes and make better decisions. This has led to increased efficiency in many areas of business.",
        expectedDetection: "medium",
        targetTier: "freemium"
    },
    {
        name: "Technical Documentation",
        text: "To implement the system, first configure the database connection. Then, set up the authentication middleware and define the API routes. Finally, test the endpoints to ensure proper functionality.",
        expectedDetection: "low",
        targetTier: "admin"
    },
    {
        name: "Academic Writing Style",
        text: "The research methodology employed in this study utilized a comprehensive approach to data collection and analysis. Consequently, the findings demonstrate significant correlations between the variables examined. Furthermore, these results contribute substantially to the existing body of knowledge in this field.",
        expectedDetection: "very-high",
        targetTier: "admin"
    }
];

/**
 * Main test runner
 */
async function runComprehensiveTests() {
    console.log('🚀 GhostLayer Comprehensive System Test Suite');
    console.log('='.repeat(60));
    console.log(`📅 Test Date: ${new Date().toISOString()}`);
    console.log(`🔧 Node Version: ${process.version}`);
    console.log('='.repeat(60));

    // Initialize services
    const humanizationService = new HumanizationService();
    const performanceManager = new TieredPerformanceManager();
    const textAnalyzer = new TextAnalyzer();
    const qualityValidator = new QualityValidator();

    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;

    // Test 1: Service Initialization
    console.log('\n📋 Test 1: Service Initialization');
    console.log('-'.repeat(40));
    
    try {
        const serviceStatus = humanizationService.getServiceStatus();
        const performanceStats = performanceManager.getAllTiers();
        
        console.log('✅ HumanizationService initialized');
        console.log(`   - Version: ${serviceStatus.version}`);
        console.log(`   - Capabilities: ${Object.keys(serviceStatus.capabilities).length}`);
        console.log(`   - Supported Tiers: ${serviceStatus.supportedTiers.length}`);
        
        console.log('✅ TieredPerformanceManager initialized');
        console.log(`   - Available Tiers: ${performanceStats.length}`);
        
        console.log('✅ TextAnalyzer initialized');
        console.log('✅ QualityValidator initialized');
        
        totalTests++;
        passedTests++;
    } catch (error) {
        console.log('❌ Service initialization failed:', error.message);
        totalTests++;
        failedTests++;
    }

    // Test 2: Tier Configuration Validation
    console.log('\n📋 Test 2: Tier Configuration Validation');
    console.log('-'.repeat(40));
    
    const tiers = ['freemium', 'premium', 'admin'];
    for (const tier of tiers) {
        try {
            const tierConfig = performanceManager.getTierConfig(tier);
            const processingConfig = performanceManager.getProcessingConfig(tier);
            
            console.log(`✅ ${tier.toUpperCase()} tier configuration valid`);
            console.log(`   - Max Passes: ${tierConfig.processing.maxPasses}`);
            console.log(`   - Aggressiveness: ${tierConfig.processing.aggressiveness}`);
            console.log(`   - Max Text Length: ${tierConfig.processing.maxTextLength}`);
            console.log(`   - Target Detection: ${tierConfig.quality.targetDetection}%`);
            
            totalTests++;
            passedTests++;
        } catch (error) {
            console.log(`❌ ${tier} tier configuration failed:`, error.message);
            totalTests++;
            failedTests++;
        }
    }

    // Test 3: Text Analysis Functionality
    console.log('\n📋 Test 3: Text Analysis Functionality');
    console.log('-'.repeat(40));
    
    for (const testCase of testCases) {
        try {
            const analysis = await textAnalyzer.analyzeText(testCase.text);
            
            if (analysis.success) {
                console.log(`✅ Analysis successful: ${testCase.name}`);
                console.log(`   - AI Detection Score: ${analysis.aiPatterns.estimatedAIDetection}%`);
                console.log(`   - Risk Level: ${analysis.risk.riskLevel}`);
                console.log(`   - Patterns Found: ${analysis.aiPatterns.patternCount}`);
                console.log(`   - High Risk Words: ${analysis.aiPatterns.highRiskWords.length}`);
                
                totalTests++;
                passedTests++;
            } else {
                throw new Error(analysis.error);
            }
        } catch (error) {
            console.log(`❌ Analysis failed for ${testCase.name}:`, error.message);
            totalTests++;
            failedTests++;
        }
    }

    // Test 4: Humanization Processing
    console.log('\n📋 Test 4: Humanization Processing');
    console.log('-'.repeat(40));
    
    for (const testCase of testCases) {
        try {
            const result = await humanizationService.humanizeText(testCase.text, {
                userTier: testCase.targetTier,
                targetDetection: 10,
                aggressiveness: 0.8
            });
            
            if (result.success) {
                console.log(`✅ Humanization successful: ${testCase.name}`);
                console.log(`   - Original Length: ${result.transformationStats.originalLength}`);
                console.log(`   - Final Length: ${result.transformationStats.finalLength}`);
                console.log(`   - Transformation Rate: ${result.transformationStats.transformationRate}%`);
                console.log(`   - Words Replaced: ${result.transformationStats.wordsReplaced}`);
                console.log(`   - Processing Time: ${result.processingTime}ms`);
                console.log(`   - AI Detection Score: ${result.qualityMetrics.aiDetectionScore}%`);
                console.log(`   - Grammar Score: ${result.qualityMetrics.grammarScore}%`);
                
                // Validate quality improvement
                const originalAnalysis = await textAnalyzer.analyzeText(testCase.text);
                const improvement = originalAnalysis.aiPatterns.estimatedAIDetection - result.qualityMetrics.aiDetectionScore;
                
                if (improvement > 0) {
                    console.log(`   - Quality Improvement: ${improvement}% reduction in AI detection`);
                } else {
                    console.log(`   - ⚠️  Warning: No significant improvement detected`);
                }
                
                totalTests++;
                passedTests++;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.log(`❌ Humanization failed for ${testCase.name}:`, error.message);
            totalTests++;
            failedTests++;
        }
    }

    // Test 5: Quality Validation
    console.log('\n📋 Test 5: Quality Validation');
    console.log('-'.repeat(40));
    
    for (const testCase of testCases) {
        try {
            const validation = await qualityValidator.validateQuality(testCase.text, 10);
            
            if (validation.success) {
                console.log(`✅ Quality validation successful: ${testCase.name}`);
                console.log(`   - Overall Score: ${validation.overallScore}/100`);
                console.log(`   - Quality Level: ${validation.qualityLevel}`);
                console.log(`   - Meets Target: ${validation.meetsTarget ? 'Yes' : 'No'}`);
                console.log(`   - AI Detection: ${validation.aiDetectionScore}%`);
                console.log(`   - Grammar Score: ${validation.grammarScore}%`);
                console.log(`   - Readability: ${validation.readabilityScore}%`);
                console.log(`   - Recommendations: ${validation.recommendations.length}`);
                
                totalTests++;
                passedTests++;
            } else {
                throw new Error(validation.error);
            }
        } catch (error) {
            console.log(`❌ Quality validation failed for ${testCase.name}:`, error.message);
            totalTests++;
            failedTests++;
        }
    }

    // Test 6: Performance Benchmarking
    console.log('\n📋 Test 6: Performance Benchmarking');
    console.log('-'.repeat(40));
    
    const benchmarkText = testCases[0].text;
    const benchmarkResults = {};
    
    for (const tier of tiers) {
        try {
            const startTime = Date.now();
            const result = await humanizationService.humanizeText(benchmarkText, {
                userTier: tier,
                targetDetection: 10,
                aggressiveness: 0.7
            });
            const endTime = Date.now();
            
            benchmarkResults[tier] = {
                processingTime: endTime - startTime,
                transformationRate: result.transformationStats?.transformationRate || 0,
                aiDetectionScore: result.qualityMetrics?.aiDetectionScore || 100,
                success: result.success
            };
            
            console.log(`✅ ${tier.toUpperCase()} tier benchmark completed`);
            console.log(`   - Processing Time: ${benchmarkResults[tier].processingTime}ms`);
            console.log(`   - Transformation Rate: ${benchmarkResults[tier].transformationRate}%`);
            console.log(`   - AI Detection Score: ${benchmarkResults[tier].aiDetectionScore}%`);
            
            totalTests++;
            passedTests++;
        } catch (error) {
            console.log(`❌ ${tier} tier benchmark failed:`, error.message);
            benchmarkResults[tier] = { success: false, error: error.message };
            totalTests++;
            failedTests++;
        }
    }

    // Test 7: Error Handling
    console.log('\n📋 Test 7: Error Handling');
    console.log('-'.repeat(40));
    
    const errorTests = [
        { name: 'Empty Text', text: '', shouldFail: true },
        { name: 'Null Text', text: null, shouldFail: true },
        { name: 'Very Short Text', text: 'Hi', shouldFail: true },
        { name: 'Very Long Text', text: 'A'.repeat(100000), shouldFail: true },
        { name: 'Invalid Tier', text: 'Valid text here', options: { userTier: 'invalid' }, shouldFail: false }
    ];
    
    for (const errorTest of errorTests) {
        try {
            const result = await humanizationService.humanizeText(errorTest.text, errorTest.options || {});
            
            if (errorTest.shouldFail && result.success) {
                console.log(`❌ Expected failure but got success: ${errorTest.name}`);
                totalTests++;
                failedTests++;
            } else if (!errorTest.shouldFail && !result.success) {
                console.log(`❌ Expected success but got failure: ${errorTest.name}`);
                totalTests++;
                failedTests++;
            } else {
                console.log(`✅ Error handling correct: ${errorTest.name}`);
                totalTests++;
                passedTests++;
            }
        } catch (error) {
            if (errorTest.shouldFail) {
                console.log(`✅ Expected error caught: ${errorTest.name}`);
                totalTests++;
                passedTests++;
            } else {
                console.log(`❌ Unexpected error: ${errorTest.name} - ${error.message}`);
                totalTests++;
                failedTests++;
            }
        }
    }

    // Final Results
    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPREHENSIVE TEST RESULTS');
    console.log('='.repeat(60));
    console.log(`✅ Passed Tests: ${passedTests}/${totalTests}`);
    console.log(`❌ Failed Tests: ${failedTests}/${totalTests}`);
    console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (failedTests === 0) {
        console.log('\n🎉 ALL TESTS PASSED! System is ready for deployment.');
    } else if (failedTests < totalTests * 0.1) {
        console.log('\n⚠️  Minor issues detected. Review failed tests before deployment.');
    } else {
        console.log('\n🚨 Significant issues detected. Address failed tests before deployment.');
    }
    
    console.log('\n📋 Performance Summary:');
    Object.entries(benchmarkResults).forEach(([tier, results]) => {
        if (results.success) {
            console.log(`   ${tier.toUpperCase()}: ${results.processingTime}ms, ${results.transformationRate}% transform, ${results.aiDetectionScore}% AI detection`);
        }
    });
    
    console.log('\n🔗 Next Steps:');
    console.log('   1. Review any failed tests and fix issues');
    console.log('   2. Test Netlify function deployment');
    console.log('   3. Validate production environment');
    console.log('   4. Monitor performance in production');
    
    return {
        totalTests,
        passedTests,
        failedTests,
        successRate: Math.round((passedTests / totalTests) * 100),
        benchmarkResults
    };
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runComprehensiveTests().catch(console.error);
}

export { runComprehensiveTests };
